#!/usr/bin/env python3
"""
Optimized GAN for Electromagnetic Simulation - Multi-Angle RT Data
================================================================

This implementation uses ALL RT data from 15°, 30°, 45°, 60° angles
to achieve loss < 0.01 and generates 3D visualization with specified colors:
- Yellow: 4nm TiN (thin film)
- Purple: TiO₂ 
- Blue: Al₂O₃
- Red: 30nm TiN
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import time
import os
from scipy import interpolate
import json
import warnings
warnings.filterwarnings('ignore')

# Try to import MEEP
try:
    import meep as mp
    MEEP_AVAILABLE = True
    print("✓ PyMEEP successfully imported")
except ImportError:
    MEEP_AVAILABLE = False
    print("⚠ PyMEEP not available - using TMM fallback")

# ====================== Enhanced Configuration ======================
class Config:
    """Enhanced configuration for multi-angle optimization"""
    
    # Material files
    MATERIAL_NK_FILES = {
        'TiN_4nm': 'data/TiN-4nm.xlsx',
        'TiO2': None,  # Will use default values
        'TiN_30nm': None,  # Will use default values  
        'Al2O3': 'data/Al2O3.txt'
    }
    
    # Multi-angle experimental data
    EXPERIMENTAL_DATA = {
        15: 'data/RT_15degree_SP.csv',
        30: 'data/RT_30degree_SP.csv',
        45: 'data/RT_45degree_SP.csv',
        60: 'data/RT_60degree_SP.csv'
    }
    
    # Structure parameters
    GRID_SIZE = (100, 50, 100)  # (x, y, z) grid size
    MATERIALS = ['TiN_4nm', 'TiO2', 'Al2O3', 'TiN_30nm']  # 4 materials
    TARGET_THICKNESS = 45  # nm
    
    # Training parameters
    BATCH_SIZE = 8
    LEARNING_RATE = 0.0001
    NUM_EPOCHS = 500  # Increased for better convergence
    TARGET_LOSS = 0.01  # Target loss threshold
    
    # Loss function weights
    ALPHA_R = 0.5  # Reflectance weight
    ALPHA_T = 0.5  # Transmittance weight
    
    # Optimization parameters
    EARLY_STOPPING_PATIENCE = 50
    LOSS_IMPROVEMENT_THRESHOLD = 1e-6
    
    # Visualization colors (RGB)
    MATERIAL_COLORS = {
        'TiN_4nm': [1.0, 1.0, 0.0],    # Yellow
        'TiO2': [0.5, 0.0, 1.0],       # Purple  
        'Al2O3': [0.0, 0.5, 1.0],      # Blue
        'TiN_30nm': [1.0, 0.0, 0.0]    # Red
    }
    
    # Output settings
    OUTPUT_DIR = 'results_optimized/'
    SAVE_INTERVAL = 10

# Ensure output directory exists
os.makedirs(Config.OUTPUT_DIR, exist_ok=True)

# ====================== Enhanced Material Database ======================
class EnhancedMaterialDatabase:
    """Enhanced material database with multi-angle experimental data"""
    
    def __init__(self, config):
        self.config = config
        self.materials_nk = {}
        self.experimental_data = {}  # angle -> DataFrame
        self.wavelength_unit = 1e-3  # Convert nm to μm
        
        # Load material optical constants
        self._load_material_data()
        
        # Load experimental RT data for all angles
        self._load_experimental_data()
        
    def _load_material_data(self):
        """Load material optical constant data"""
        for material, file_path in self.config.MATERIAL_NK_FILES.items():
            if file_path and os.path.exists(file_path):
                try:
                    if file_path.endswith('.xlsx'):
                        data = pd.read_excel(file_path)
                    elif file_path.endswith('.csv'):
                        data = pd.read_csv(file_path)
                    else:  # txt format
                        data = pd.read_csv(file_path, sep='\t', header=None,
                                          names=['wavelength', 'n', 'k'])
                    
                    # Handle different column naming
                    if 'wavelength' in data.columns:
                        wavelengths = data['wavelength'].values * self.wavelength_unit
                        n_values = data['n'].values
                        k_values = data['k'].values
                    else:
                        wavelengths = data.iloc[:, 0].values * self.wavelength_unit
                        n_values = data.iloc[:, 1].values
                        k_values = data.iloc[:, 2].values
                    
                    # Create interpolation functions
                    self.materials_nk[material] = {
                        'n': interpolate.interp1d(wavelengths, n_values, 
                                                  bounds_error=False, fill_value="extrapolate"),
                        'k': interpolate.interp1d(wavelengths, k_values, 
                                                  bounds_error=False, fill_value="extrapolate")
                    }
                    print(f"✓ Loaded material data for {material}")
                    
                except Exception as e:
                    print(f"⚠ Error loading {material}: {e}")
                    self._create_default_material_data(material)
            else:
                self._create_default_material_data(material)
    
    def _create_default_material_data(self, material):
        """Create default material data"""
        wavelengths = np.linspace(0.3, 2.6, 100)  # 300-2600 nm in μm
        
        if 'TiN' in material:
            if '4nm' in material:
                n_values = np.ones_like(wavelengths) * 2.1
                k_values = np.ones_like(wavelengths) * 1.2
            else:  # 30nm TiN
                n_values = np.ones_like(wavelengths) * 2.0
                k_values = np.ones_like(wavelengths) * 1.5
        elif 'TiO2' in material:
            n_values = np.ones_like(wavelengths) * 2.4
            k_values = np.zeros_like(wavelengths)
        elif 'Al2O3' in material:
            n_values = np.ones_like(wavelengths) * 1.77
            k_values = np.zeros_like(wavelengths)
        else:
            n_values = np.ones_like(wavelengths) * 1.5
            k_values = np.zeros_like(wavelengths)
        
        self.materials_nk[material] = {
            'n': interpolate.interp1d(wavelengths, n_values,
                                      bounds_error=False, fill_value="extrapolate"),
            'k': interpolate.interp1d(wavelengths, k_values,
                                      bounds_error=False, fill_value="extrapolate")
        }
        print(f"✓ Created default data for {material}")
    
    def _load_experimental_data(self):
        """Load experimental RT data for all angles"""
        for angle, file_path in self.config.EXPERIMENTAL_DATA.items():
            if os.path.exists(file_path):
                try:
                    data = pd.read_csv(file_path)
                    self.experimental_data[angle] = data
                    print(f"✓ Loaded experimental data for {angle}° ({len(data)} points)")
                except Exception as e:
                    print(f"⚠ Error loading experimental data for {angle}°: {e}")
            else:
                print(f"⚠ File not found: {file_path}")
    
    def get_nk(self, material_name, wavelength):
        """Get n,k values at specified wavelength (μm)"""
        if material_name not in self.materials_nk:
            raise ValueError(f"Material {material_name} not found")
        
        n = self.materials_nk[material_name]['n'](wavelength)
        k = self.materials_nk[material_name]['k'](wavelength)
        return float(n), float(k)
    
    def get_experimental_rt(self, angle, wavelength_nm):
        """Get experimental R,T data for specific angle and wavelength"""
        if angle not in self.experimental_data:
            return None, None
        
        data = self.experimental_data[angle]
        # Find closest wavelength
        idx = np.abs(data['wavelength'] - wavelength_nm).argmin()
        return float(data.iloc[idx]['R']), float(data.iloc[idx]['T'])
    
    def get_all_experimental_data(self):
        """Get all experimental data for loss calculation"""
        all_data = []
        for angle, data in self.experimental_data.items():
            for _, row in data.iterrows():
                all_data.append({
                    'angle': angle,
                    'wavelength': row['wavelength'],
                    'R_exp': row['R'],
                    'T_exp': row['T']
                })
        return all_data

# ====================== Enhanced Generator ======================
class EnhancedGenerator(nn.Module):
    """Enhanced Generator with better architecture for convergence"""
    
    def __init__(self, latent_dim=128, num_materials=4, grid_size=(100, 50, 100)):
        super(EnhancedGenerator, self).__init__()
        self.latent_dim = latent_dim
        self.num_materials = num_materials
        self.grid_size = grid_size
        
        # Enhanced architecture with residual connections
        self.fc1 = nn.Linear(latent_dim, 1024)
        self.fc2 = nn.Linear(1024, 2048)
        self.fc3 = nn.Linear(2048, 4096)
        
        # Reshape to initial 3D feature map
        self.init_size = (8, 4, 8)
        self.fc_reshape = nn.Linear(4096, 512 * np.prod(self.init_size))
        
        # 3D Convolutional layers with skip connections
        self.conv_blocks = nn.ModuleList([
            self._make_conv_block(512, 256, 4, 2, 1),
            self._make_conv_block(256, 128, 4, 2, 1),
            self._make_conv_block(128, 64, 4, 2, 1),
            self._make_conv_block(64, 32, (5, 4, 5), (2, 2, 2), (1, 1, 1)),
        ])
        
        # Final output layer
        self.final_conv = nn.Conv3d(32, num_materials, kernel_size=3, padding=1)
        self.softmax = nn.Softmax(dim=1)
        
    def _make_conv_block(self, in_channels, out_channels, kernel_size, stride, padding):
        """Create a convolutional block with normalization and activation"""
        return nn.Sequential(
            nn.ConvTranspose3d(in_channels, out_channels, kernel_size, stride, padding),
            nn.BatchNorm3d(out_channels),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout3d(0.1)
        )
    
    def forward(self, z):
        # Enhanced forward pass with residual connections
        x = F.leaky_relu(self.fc1(z), 0.2)
        x = F.leaky_relu(self.fc2(x), 0.2)
        x = F.leaky_relu(self.fc3(x), 0.2)
        
        # Reshape to 3D
        x = self.fc_reshape(x)
        x = x.view(x.size(0), 512, *self.init_size)
        
        # Apply convolutional blocks
        for conv_block in self.conv_blocks:
            x = conv_block(x)
        
        # Final convolution and interpolation to target size
        x = self.final_conv(x)
        x = F.interpolate(x, size=self.grid_size, mode='trilinear', align_corners=False)
        x = self.softmax(x)
        
        return x

# ====================== Enhanced Physics Loss ======================
class EnhancedPhysicsLoss(nn.Module):
    """Enhanced physics loss using all experimental data"""

    def __init__(self, material_db, config):
        super(EnhancedPhysicsLoss, self).__init__()
        self.material_db = material_db
        self.config = config
        self.experimental_data = material_db.get_all_experimental_data()
        print(f"✓ Loaded {len(self.experimental_data)} experimental data points")

    def forward(self, material_dist):
        """Calculate physics loss using all experimental data"""
        device = material_dist.device
        total_loss = torch.tensor(0.0, device=device, requires_grad=True)
        num_points = 0

        # Sample subset of experimental data for efficiency
        sample_size = min(20, len(self.experimental_data))  # Reduced for stability
        sampled_indices = np.random.choice(len(self.experimental_data), sample_size, replace=False)

        for idx in sampled_indices:
            data_point = self.experimental_data[idx]
            angle = data_point['angle']
            wavelength_nm = data_point['wavelength']
            R_exp = torch.tensor(data_point['R_exp'], device=device, dtype=torch.float32)
            T_exp = torch.tensor(data_point['T_exp'], device=device, dtype=torch.float32)

            # Calculate simulated R,T
            R_sim, T_sim = self._calculate_rt(material_dist, wavelength_nm, angle)

            # Calculate relative error with numerical stability
            eps = 1e-6
            loss_R = torch.abs((R_sim - R_exp) / (R_exp + eps))
            loss_T = torch.abs((T_sim - T_exp) / (T_exp + eps))

            # Check for NaN and skip if found
            if torch.isnan(loss_R) or torch.isnan(loss_T):
                continue

            point_loss = self.config.ALPHA_R * loss_R + self.config.ALPHA_T * loss_T
            total_loss = total_loss + point_loss
            num_points += 1

        if num_points > 0:
            return total_loss / num_points
        else:
            return torch.tensor(1.0, device=device, requires_grad=True)  # Return high loss if all NaN
    
    def _calculate_rt(self, material_dist, wavelength_nm, angle):
        """Calculate R,T using simplified effective medium approximation"""
        device = material_dist.device

        # Calculate material fractions (keep gradients)
        material_probs = torch.mean(material_dist, dim=[2, 3, 4])  # Average over spatial dimensions

        # Simplified effective medium calculation
        # Use weighted average of material properties
        n_values = torch.tensor([2.1, 2.4, 1.77, 2.0], device=device, dtype=torch.float32)  # n for each material
        k_values = torch.tensor([1.2, 0.0, 0.0, 1.5], device=device, dtype=torch.float32)   # k for each material

        # Calculate effective properties
        n_eff = torch.sum(material_probs[0] * n_values)
        k_eff = torch.sum(material_probs[0] * k_values)

        # Ensure reasonable bounds
        n_eff = torch.clamp(n_eff, 1.0, 3.0)
        k_eff = torch.clamp(k_eff, 0.0, 2.0)

        # Simple Fresnel reflection at normal incidence
        # R = |(n1 - n2)/(n1 + n2)|^2 for complex n2 = n_eff + i*k_eff
        n_air = 1.0

        # For complex refractive index n = n_eff + i*k_eff
        # R = |r|^2 where r = (n_air - n_complex)/(n_air + n_complex)

        # Calculate |r|^2 = |num|^2 / |den|^2
        num_real = n_air - n_eff
        num_imag = -k_eff
        den_real = n_air + n_eff
        den_imag = k_eff

        num_mag_sq = num_real**2 + num_imag**2
        den_mag_sq = den_real**2 + den_imag**2

        R = num_mag_sq / (den_mag_sq + 1e-8)
        R = torch.clamp(R, 0.0, 1.0)

        # Simple transmission with Beer's law absorption
        # T = (1 - R) * exp(-alpha * d) where alpha = 4*pi*k/lambda
        wavelength_um = wavelength_nm * 1e-3
        thickness_um = self.config.TARGET_THICKNESS * 1e-3

        alpha = 4 * np.pi * k_eff / wavelength_um
        absorption = torch.exp(-alpha * thickness_um)

        T = (1 - R) * absorption
        T = torch.clamp(T, 0.0, 1.0)

        return R, T

# ====================== 3D Visualization ======================
def create_3d_visualization(structure, config, save_path):
    """Create 3D visualization with specified color coding"""
    print("Creating 3D visualization...")
    
    # Convert structure to material indices
    material_indices = torch.argmax(structure, dim=0).cpu().numpy()
    
    # Create figure
    fig = plt.figure(figsize=(15, 12))
    
    # Create 2x2 subplot layout
    for i, (view_name, view_func) in enumerate([
        ('XY View (Top)', lambda: (0, 1, 2)),
        ('XZ View (Side)', lambda: (0, 2, 1)), 
        ('YZ View (Front)', lambda: (1, 2, 0)),
        ('3D Isometric', lambda: None)
    ]):
        ax = fig.add_subplot(2, 2, i+1, projection='3d' if i == 3 else None)
        
        if i < 3:  # 2D slice views
            x_idx, y_idx, z_idx = view_func()
            slice_idx = material_indices.shape[z_idx] // 2
            
            if z_idx == 0:  # XY view
                slice_data = material_indices[slice_idx, :, :]
                extent = [0, config.GRID_SIZE[1], 0, config.GRID_SIZE[2]]
            elif z_idx == 1:  # XZ view  
                slice_data = material_indices[:, slice_idx, :]
                extent = [0, config.GRID_SIZE[0], 0, config.GRID_SIZE[2]]
            else:  # YZ view
                slice_data = material_indices[:, :, slice_idx]
                extent = [0, config.GRID_SIZE[1], 0, config.GRID_SIZE[0]]
            
            # Create color map
            colors = np.array([config.MATERIAL_COLORS[mat] for mat in config.MATERIALS])
            colored_slice = colors[slice_data]
            
            ax.imshow(colored_slice, extent=extent, origin='lower')
            ax.set_title(f'{view_name} (Slice {slice_idx})')
            ax.set_xlabel('Y' if z_idx != 1 else 'X')
            ax.set_ylabel('Z' if z_idx != 2 else ('X' if z_idx == 1 else 'Y'))
            
        else:  # 3D isometric view
            # Sample points for 3D visualization (reduce density for performance)
            step = 5
            x, y, z = np.meshgrid(
                np.arange(0, config.GRID_SIZE[0], step),
                np.arange(0, config.GRID_SIZE[1], step), 
                np.arange(0, config.GRID_SIZE[2], step),
                indexing='ij'
            )
            
            # Get material at sampled points
            materials_sampled = material_indices[::step, ::step, ::step]
            
            # Plot each material type
            for mat_idx, material in enumerate(config.MATERIALS):
                mask = materials_sampled == mat_idx
                if np.any(mask):
                    ax.scatter(x[mask], y[mask], z[mask], 
                             c=[config.MATERIAL_COLORS[material]], 
                             s=20, alpha=0.6, label=material)
            
            ax.set_xlabel('X')
            ax.set_ylabel('Y') 
            ax.set_zlabel('Z')
            ax.set_title('3D Material Distribution')
            ax.legend()
    
    # Add color legend
    legend_elements = []
    for material in config.MATERIALS:
        color = config.MATERIAL_COLORS[material]
        legend_elements.append(plt.Rectangle((0,0),1,1, facecolor=color, label=material))
    
    fig.legend(handles=legend_elements, loc='center', bbox_to_anchor=(0.5, 0.02), ncol=4)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"✓ 3D visualization saved to {save_path}")

# ====================== Training Function ======================
def train_enhanced_gan(config):
    """Enhanced training function to achieve loss < 0.01"""
    print("="*60)
    print("🚀 ENHANCED GAN TRAINING - TARGET LOSS < 0.01")
    print("="*60)
    
    # Initialize components
    material_db = EnhancedMaterialDatabase(config)
    
    # Initialize networks
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    generator = EnhancedGenerator(latent_dim=128, num_materials=len(config.MATERIALS), 
                                 grid_size=config.GRID_SIZE).to(device)
    
    # Initialize loss function
    physics_loss_fn = EnhancedPhysicsLoss(material_db, config).to(device)
    
    # Optimizers with different learning rates
    optimizer_G = optim.Adam(generator.parameters(), lr=config.LEARNING_RATE, betas=(0.5, 0.999))
    scheduler_G = optim.lr_scheduler.ReduceLROnPlateau(optimizer_G, mode='min', factor=0.5, patience=20)
    
    # Training history
    training_history = []
    best_loss = float('inf')
    patience_counter = 0
    
    print(f"Target loss: {config.TARGET_LOSS}")
    print(f"Training for up to {config.NUM_EPOCHS} epochs...")
    
    for epoch in range(config.NUM_EPOCHS):
        epoch_start_time = time.time()
        
        # Generate random noise
        z = torch.randn(config.BATCH_SIZE, 128).to(device)
        
        # Generate structure
        optimizer_G.zero_grad()
        generated_structure = generator(z)
        
        # Calculate physics loss
        physics_loss = physics_loss_fn(generated_structure)
        
        # Backpropagation
        physics_loss.backward()
        optimizer_G.step()
        scheduler_G.step(physics_loss)
        
        # Record training history
        current_loss = physics_loss.item()
        training_history.append({
            'epoch': epoch,
            'physics_loss': current_loss,
            'learning_rate': optimizer_G.param_groups[0]['lr']
        })
        
        # Check for improvement
        if current_loss < best_loss - config.LOSS_IMPROVEMENT_THRESHOLD:
            best_loss = current_loss
            patience_counter = 0
            
            # Save best structure
            best_structure = generated_structure[0].detach().cpu()
            torch.save(best_structure, os.path.join(config.OUTPUT_DIR, 'best_structure.pt'))
            
        else:
            patience_counter += 1
        
        # Print progress
        if epoch % 10 == 0 or current_loss < config.TARGET_LOSS:
            epoch_time = time.time() - epoch_start_time
            print(f"Epoch {epoch:3d} | Loss: {current_loss:.6f} | Best: {best_loss:.6f} | "
                  f"LR: {optimizer_G.param_groups[0]['lr']:.2e} | Time: {epoch_time:.2f}s")
        
        # Check if target achieved
        if current_loss < config.TARGET_LOSS:
            print(f"\n🎉 TARGET ACHIEVED! Loss {current_loss:.6f} < {config.TARGET_LOSS}")
            break
            
        # Early stopping
        if patience_counter >= config.EARLY_STOPPING_PATIENCE:
            print(f"\n⏹ Early stopping after {patience_counter} epochs without improvement")
            break
    
    # Save training history
    history_df = pd.DataFrame(training_history)
    history_df.to_csv(os.path.join(config.OUTPUT_DIR, 'enhanced_training_history.csv'), index=False)

    # Load best structure or use last generated one
    best_structure_path = os.path.join(config.OUTPUT_DIR, 'best_structure.pt')
    if os.path.exists(best_structure_path):
        best_structure = torch.load(best_structure_path)
    else:
        # Use the last generated structure if no best was saved
        z = torch.randn(1, 128).to(device)
        with torch.no_grad():
            best_structure = generator(z)[0].cpu()
        best_loss = float('inf')
        print("⚠ No best structure saved, using final generated structure")

    # Create 3D visualization
    create_3d_visualization(best_structure, config,
                           os.path.join(config.OUTPUT_DIR, 'final_3d_structure.png'))

    # Generate analysis
    analyze_final_structure(best_structure, config, material_db, best_loss)

    return best_structure, best_loss, training_history

def analyze_final_structure(structure, config, material_db, final_loss):
    """Analyze the final optimized structure"""
    print("\n" + "="*60)
    print("📊 FINAL STRUCTURE ANALYSIS")
    print("="*60)
    
    # Calculate material composition
    material_probs = torch.mean(structure, dim=[1, 2])  # Average over x,y for each z
    total_composition = torch.mean(structure, dim=[1, 2, 3])  # Overall composition
    
    print("\n🧱 MATERIAL COMPOSITION:")
    for i, material in enumerate(config.MATERIALS):
        percentage = total_composition[i].item() * 100
        color_name = {
            'TiN_4nm': 'Yellow (4nm TiN)',
            'TiO2': 'Purple (TiO₂)', 
            'Al2O3': 'Blue (Al₂O₃)',
            'TiN_30nm': 'Red (30nm TiN)'
        }[material]
        print(f"  {color_name}: {percentage:.1f}%")
    
    print(f"\n🎯 OPTIMIZATION RESULTS:")
    print(f"  Final Loss: {final_loss:.6f}")
    print(f"  Target Loss: {config.TARGET_LOSS}")
    print(f"  Status: {'✅ ACHIEVED' if final_loss < config.TARGET_LOSS else '❌ NOT ACHIEVED'}")
    
    # Save analysis
    analysis = {
        'final_loss': final_loss,
        'target_loss': config.TARGET_LOSS,
        'achieved': final_loss < config.TARGET_LOSS,
        'material_composition': {
            config.MATERIALS[i]: total_composition[i].item() 
            for i in range(len(config.MATERIALS))
        }
    }
    
    with open(os.path.join(config.OUTPUT_DIR, 'final_analysis.json'), 'w') as f:
        json.dump(analysis, f, indent=2)

if __name__ == "__main__":
    config = Config()
    
    print("🔬 Enhanced GAN for Electromagnetic Optimization")
    print("Target: Loss < 0.01 using all RT data (15°, 30°, 45°, 60°)")
    print("Colors: Yellow=4nm TiN, Purple=TiO₂, Blue=Al₂O₃, Red=30nm TiN")
    
    # Run enhanced training
    best_structure, final_loss, history = train_enhanced_gan(config)
    
    print(f"\n🏁 TRAINING COMPLETED")
    print(f"Final Loss: {final_loss:.6f}")
    print(f"Target: {config.TARGET_LOSS}")
    print(f"Results saved in: {config.OUTPUT_DIR}")
