#!/usr/bin/env python3
"""
MEEP with FIXED Flux Normalization
===================================
Proper MEEP flux normalization using reference simulation
"""

import numpy as np
import pandas as pd
from pathlib import Path
import meep as mp
import time

print(f"✅ MEEP v{mp.__version__} - FIXED NORMALIZATION")

class MaterialDatabase:
    """Load material data"""
    
    def __init__(self):
        self.materials = {}
        self.load_materials()
    
    def load_materials(self):
        """Load materials"""
        
        # Load Al2O3
        try:
            al2o3_file = Path("data/Al2O3.txt")
            if al2o3_file.exists():
                data = pd.read_csv(al2o3_file, sep='\s+', header=None, names=['wavelength', 'n', 'k'])
                self.materials['Al2O3'] = data
                print(f"✓ Al2O3 loaded: {len(data)} points")
            else:
                raise FileNotFoundError("Al2O3.txt required")
        except Exception as e:
            print(f"❌ Al2O3 loading failed - {e}")
            exit(1)
        
        # Load TiN-4nm
        try:
            tin_file = Path("data/TiN-4nm.xlsx")
            if tin_file.exists():
                data = pd.read_excel(tin_file)
                data['wavelength'] = data['Wavelength'] * 1e9
                data = data[['wavelength', 'n', 'k']].copy()
                data = data.dropna()
                
                self.materials['TiN_4nm'] = data
                print(f"✓ TiN-4nm loaded: {len(data)} points")
            else:
                raise FileNotFoundError("TiN-4nm.xlsx required")
        except Exception as e:
            print(f"❌ TiN-4nm loading failed - {e}")
            exit(1)
    
    def get_refractive_index(self, material, wavelength_nm):
        """Get complex refractive index"""
        if material not in self.materials:
            raise ValueError(f"Material {material} not found")
        
        data = self.materials[material]
        n = np.interp(wavelength_nm, data['wavelength'], data['n'])
        k = np.interp(wavelength_nm, data['wavelength'], data['k'])
        
        if np.isnan(n) or np.isnan(k):
            raise ValueError(f"Invalid n/k for {material} at {wavelength_nm}nm")
        
        return n + 1j * k

class MEEPSimulatorFixed:
    """MEEP simulator with FIXED flux normalization"""
    
    def __init__(self, material_db):
        self.material_db = material_db
        
        # Structure dimensions (micrometers)
        self.pml_thickness = 0.3
        self.air_gap = 0.5
        self.material_thickness = 0.045
        self.lateral_size = 1.0
        self.total_size = 2 * self.pml_thickness + 2 * self.air_gap + self.material_thickness
        
        print(f"✓ MEEP structure: {self.total_size*1000:.0f}nm total")
    
    def create_meep_material(self, material_name, wavelength_nm):
        """Create MEEP material"""
        n_complex = self.material_db.get_refractive_index(material_name, wavelength_nm)
        epsilon = n_complex ** 2
        
        if n_complex.imag == 0:
            return mp.Medium(epsilon=epsilon.real)
        else:
            conductivity = 2 * np.pi * n_complex.imag / (wavelength_nm / 1000)
            return mp.Medium(epsilon=epsilon.real, D_conductivity=conductivity)
    
    def run_reference_simulation(self, wavelength_nm, resolution=20):
        """Run reference simulation (no material) to get incident flux"""
        try:
            print("  Running reference simulation (no material)...")
            
            wavelength_um = wavelength_nm / 1000.0
            frequency = 1.0 / wavelength_um
            
            # Empty geometry (air only)
            geometry = []
            
            # Simulation setup
            cell = mp.Vector3(self.lateral_size, self.lateral_size, self.total_size)
            pml_layers = [mp.PML(thickness=self.pml_thickness)]
            
            # Source
            source_z = -self.material_thickness/2 - self.air_gap/2
            sources = [mp.Source(
                mp.GaussianSource(frequency=frequency, fwidth=frequency*0.1),
                component=mp.Ex,
                center=mp.Vector3(0, 0, source_z)
            )]
            
            # Create simulation
            sim = mp.Simulation(
                cell_size=cell,
                boundary_layers=pml_layers,
                geometry=geometry,
                sources=sources,
                resolution=resolution,
                force_complex_fields=True
            )
            
            # Flux monitor at transmission location
            trans_z = self.material_thickness/2 + self.air_gap/2
            trans_region = mp.FluxRegion(
                center=mp.Vector3(0, 0, trans_z),
                size=mp.Vector3(self.lateral_size*0.8, self.lateral_size*0.8, 0)
            )
            incident_flux = sim.add_flux(frequency, 0, 1, trans_region)
            
            # Run simulation
            sim.run(until=100)  # Shorter run for reference
            
            # Get incident flux
            incident_data = mp.get_fluxes(incident_flux)
            if len(incident_data) == 0:
                raise ValueError("No incident flux data")
            
            incident_power = abs(incident_data[0])
            print(f"  ✓ Reference incident flux: {incident_power:.6f}")
            
            return incident_power
            
        except Exception as e:
            print(f"❌ Reference simulation failed: {e}")
            raise
    
    def simulate_structure(self, material_name, wavelength_nm, angle_deg=0, resolution=20):
        """MEEP simulation with FIXED normalization"""
        try:
            print(f"\n🔬 MEEP Simulation (FIXED): {material_name} @ {wavelength_nm}nm, {angle_deg}°")
            
            # Get incident flux from reference simulation
            incident_power = self.run_reference_simulation(wavelength_nm, resolution)
            
            # Now run simulation with material
            print("  Running material simulation...")
            
            wavelength_um = wavelength_nm / 1000.0
            frequency = 1.0 / wavelength_um
            
            # Create material
            material = self.create_meep_material(material_name, wavelength_nm)
            
            # Geometry with material
            geometry = [mp.Block(
                center=mp.Vector3(0, 0, 0),
                size=mp.Vector3(self.lateral_size, self.lateral_size, self.material_thickness),
                material=material
            )]
            
            # Simulation setup
            cell = mp.Vector3(self.lateral_size, self.lateral_size, self.total_size)
            pml_layers = [mp.PML(thickness=self.pml_thickness)]
            
            # Source
            source_z = -self.material_thickness/2 - self.air_gap/2
            sources = [mp.Source(
                mp.GaussianSource(frequency=frequency, fwidth=frequency*0.1),
                component=mp.Ex,
                center=mp.Vector3(0, 0, source_z)
            )]
            
            # Create simulation
            sim = mp.Simulation(
                cell_size=cell,
                boundary_layers=pml_layers,
                geometry=geometry,
                sources=sources,
                resolution=resolution,
                force_complex_fields=True
            )
            
            # Flux monitors
            refl_z = source_z + self.air_gap/4
            refl_region = mp.FluxRegion(
                center=mp.Vector3(0, 0, refl_z),
                size=mp.Vector3(self.lateral_size*0.8, self.lateral_size*0.8, 0)
            )
            refl_flux = sim.add_flux(frequency, 0, 1, refl_region)
            
            trans_z = self.material_thickness/2 + self.air_gap/2
            trans_region = mp.FluxRegion(
                center=mp.Vector3(0, 0, trans_z),
                size=mp.Vector3(self.lateral_size*0.8, self.lateral_size*0.8, 0)
            )
            trans_flux = sim.add_flux(frequency, 0, 1, trans_region)
            
            # Run simulation
            sim.run(until=100)  # Shorter run for testing
            
            # Get flux values
            refl_data = mp.get_fluxes(refl_flux)
            trans_data = mp.get_fluxes(trans_flux)
            
            if len(refl_data) == 0 or len(trans_data) == 0:
                raise ValueError("No flux data obtained")
            
            # FIXED normalization using incident flux
            R = abs(refl_data[0]) / incident_power
            T = abs(trans_data[0]) / incident_power
            
            # Ensure physical bounds
            R = max(0, min(1, abs(R)))
            T = max(0, min(1, abs(T)))
            
            print(f"  ✓ MEEP Results (FIXED): R = {R:.6f}, T = {T:.6f}, R+T = {R+T:.6f}")
            
            return R, T
            
        except Exception as e:
            print(f"❌ MEEP simulation failed: {e}")
            raise

def test_fixed_meep():
    """Test MEEP with fixed normalization"""
    print("\n🔧 Testing MEEP with FIXED Normalization")
    print("=" * 60)
    
    # Initialize
    material_db = MaterialDatabase()
    meep_sim = MEEPSimulatorFixed(material_db)
    
    # Test Al2O3
    try:
        R_meep, T_meep = meep_sim.simulate_structure('Al2O3', 800, 0, resolution=10)  # Lower resolution for speed
        
        # Compare with expected values (rough estimates)
        print(f"\n📊 FIXED MEEP Results:")
        print(f"  R = {R_meep:.6f}")
        print(f"  T = {T_meep:.6f}")
        print(f"  R+T = {R_meep + T_meep:.6f}")
        
        # Check energy conservation
        if R_meep + T_meep <= 1.01:
            print("✅ Energy conservation: PASS")
        else:
            print("❌ Energy conservation: FAIL")
        
        # Check physical bounds
        if 0 <= R_meep <= 1 and 0 <= T_meep <= 1:
            print("✅ Physical bounds: PASS")
        else:
            print("❌ Physical bounds: FAIL")
        
        return True
        
    except Exception as e:
        print(f"❌ Fixed MEEP test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_fixed_meep()
    if success:
        print("\n🎉 MEEP NORMALIZATION FIXED!")
        print("✅ Ready for validation protocol")
    else:
        print("\n❌ MEEP normalization still needs work")
