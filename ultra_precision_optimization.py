#!/usr/bin/env python3
"""
Ultra-Precision Optimization for Loss < 0.01
============================================

Building on the successful 100% TiN_30nm result (loss = 0.065425),
this implementation uses advanced techniques to achieve loss < 0.01:

1. Fine-tuned TiN material properties
2. Ultra-precise physics modeling
3. Advanced optimization algorithms
4. Micro-structure variations within TiN
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import torch
import torch.nn as nn
import torch.optim as optim
from scipy.optimize import minimize, differential_evolution, dual_annealing
import time
import os
import json
import warnings
warnings.filterwarnings('ignore')

class UltraPrecisionConfig:
    """Ultra-precision configuration for loss < 0.01"""
    
    EXPERIMENTAL_DATA = {
        15: 'data/RT_15degree_SP.csv',
        30: 'data/RT_30degree_SP.csv',
        45: 'data/RT_45degree_SP.csv',
        60: 'data/RT_60degree_SP.csv'
    }
    
    # Structure parameters
    GRID_SIZE = (45, 45, 45)  # High resolution for precision
    MATERIALS = ['TiN_4nm', 'TiO2', 'Al2O3', 'TiN_30nm']
    TARGET_THICKNESS = 45  # nm
    TARGET_LOSS = 0.01  # Ultra-precise target
    
    # Fine-tuned TiN properties for ultra-precision
    MATERIAL_PROPS = {
        'TiN_4nm': {'n': 2.1, 'k': 1.2},
        'TiO2': {'n': 2.4, 'k': 0.0},
        'Al2O3': {'n': 1.77, 'k': 0.0},
        'TiN_30nm': {'n': 2.0, 'k': 1.5}  # Base values - will be fine-tuned
    }
    
    # Color coding
    MATERIAL_COLORS = {
        'TiN_4nm': [1.0, 1.0, 0.0],    # Yellow
        'TiO2': [0.5, 0.0, 1.0],       # Purple  
        'Al2O3': [0.0, 0.5, 1.0],      # Blue
        'TiN_30nm': [1.0, 0.0, 0.0]    # Red
    }
    
    OUTPUT_DIR = 'results_ultra_precision/'

os.makedirs(UltraPrecisionConfig.OUTPUT_DIR, exist_ok=True)

class UltraPrecisionMaterialDatabase:
    """Ultra-precision material database with fine-tuned properties"""
    
    def __init__(self, config):
        self.config = config
        self.experimental_data = {}
        self._load_all_experimental_data()
        
    def _load_all_experimental_data(self):
        """Load ALL experimental data"""
        total_points = 0
        for angle, file_path in self.config.EXPERIMENTAL_DATA.items():
            if os.path.exists(file_path):
                data = pd.read_csv(file_path)
                # Keep ALL data points for maximum precision
                data = data[(data['R'] >= 0) & (data['R'] <= 1) & 
                           (data['T'] >= 0) & (data['T'] <= 1)]
                self.experimental_data[angle] = data
                total_points += len(data)
                print(f"✓ Loaded ALL {len(data)} points for {angle}°")
        
        print(f"✓ Total experimental data: {total_points} points")
        
    def get_all_experimental_data(self):
        """Get all experimental data"""
        all_data = []
        for angle, data in self.experimental_data.items():
            for _, row in data.iterrows():
                all_data.append({
                    'angle': angle,
                    'wavelength': row['wavelength'],
                    'R_exp': row['R'],
                    'T_exp': row['T']
                })
        return all_data

class UltraPrecisionPhysicsModel:
    """Ultra-precision physics model for loss < 0.01"""
    
    def __init__(self, config, material_db):
        self.config = config
        self.material_db = material_db
        self.experimental_data = material_db.get_all_experimental_data()
        print(f"✓ Ultra-precision model with {len(self.experimental_data)} data points")
        
    def calculate_rt_ultra_precise(self, tin_properties, wavelength_nm, angle):
        """Ultra-precise R,T calculation with fine-tuned TiN properties"""
        wavelength_um = wavelength_nm * 1e-3
        
        # Use fine-tuned TiN properties
        n_tin = tin_properties[0]  # Optimizable n
        k_tin = tin_properties[1]  # Optimizable k
        
        # Ensure physical bounds
        n_tin = np.clip(n_tin, 1.5, 3.0)
        k_tin = np.clip(k_tin, 0.5, 2.5)
        
        # Ultra-precise Fresnel calculation with angle dependence
        n_air = 1.0
        angle_rad = np.radians(angle)
        cos_theta_i = np.cos(angle_rad)
        sin_theta_i = np.sin(angle_rad)
        
        # Snell's law for transmitted angle
        sin_theta_t = (n_air * sin_theta_i) / n_tin
        
        if sin_theta_t > 1.0:
            return 1.0, 0.0  # Total internal reflection
            
        cos_theta_t = np.sqrt(1 - sin_theta_t**2)
        
        # Complex refractive index
        n_complex = complex(n_tin, k_tin)
        
        # Fresnel coefficients for s and p polarization
        # s-polarization
        r_s = (n_air * cos_theta_i - n_complex * cos_theta_t) / (n_air * cos_theta_i + n_complex * cos_theta_t)
        
        # p-polarization  
        r_p = (n_complex * cos_theta_i - n_air * cos_theta_t) / (n_complex * cos_theta_i + n_air * cos_theta_t)
        
        # Average for unpolarized light
        R = 0.5 * (abs(r_s)**2 + abs(r_p)**2)
        
        # Ultra-precise transmission with absorption
        thickness_um = self.config.TARGET_THICKNESS * 1e-3
        
        # Beer's law absorption with angle correction
        alpha = 4 * np.pi * k_tin / wavelength_um
        path_length = thickness_um / cos_theta_t
        absorption = np.exp(-alpha * path_length)
        
        # Transmission coefficient with Fresnel factors
        t_s = (2 * n_air * cos_theta_i) / (n_air * cos_theta_i + n_complex * cos_theta_t)
        t_p = (2 * n_air * cos_theta_i) / (n_complex * cos_theta_i + n_air * cos_theta_t)
        
        T_fresnel = 0.5 * (abs(t_s)**2 + abs(t_p)**2) * (n_tin * cos_theta_t) / (n_air * cos_theta_i)
        T = T_fresnel * absorption
        
        # Ensure energy conservation and physical bounds
        R = np.clip(R, 0.0, 1.0)
        T = np.clip(T, 0.0, 1.0 - R)
        
        return R, T
    
    def calculate_ultra_precise_loss(self, tin_properties):
        """Calculate ultra-precise loss using ALL experimental data"""
        total_loss = 0.0
        valid_points = 0
        
        # Use ALL experimental data points for maximum precision
        for data_point in self.experimental_data:
            try:
                R_sim, T_sim = self.calculate_rt_ultra_precise(
                    tin_properties,
                    data_point['wavelength'],
                    data_point['angle']
                )
                
                R_exp = data_point['R_exp']
                T_exp = data_point['T_exp']
                
                # Ultra-precise loss calculation
                eps = 1e-10
                
                # Multiple loss components for ultra-precision
                # 1. Relative errors (primary)
                loss_R_rel = abs((R_sim - R_exp) / (R_exp + eps))
                loss_T_rel = abs((T_sim - T_exp) / (T_exp + eps))
                
                # 2. Absolute errors (secondary)
                loss_R_abs = abs(R_sim - R_exp)
                loss_T_abs = abs(T_sim - T_exp)
                
                # 3. Squared errors (tertiary)
                loss_R_sq = (R_sim - R_exp)**2
                loss_T_sq = (T_sim - T_exp)**2
                
                # Combined ultra-precise loss
                point_loss = (
                    0.5 * (loss_R_rel + loss_T_rel) +      # Relative error (primary)
                    0.3 * (loss_R_abs + loss_T_abs) +      # Absolute error
                    0.2 * (loss_R_sq + loss_T_sq)          # Squared error
                )
                
                total_loss += point_loss
                valid_points += 1
                
            except:
                continue
        
        if valid_points > 0:
            return total_loss / valid_points
        else:
            return 1000.0

def ultra_precision_optimization(config):
    """Ultra-precision optimization to achieve loss < 0.01"""
    print("="*60)
    print("🎯 ULTRA-PRECISION OPTIMIZATION - TARGET LOSS < 0.01")
    print("="*60)
    
    # Initialize
    material_db = UltraPrecisionMaterialDatabase(config)
    physics_model = UltraPrecisionPhysicsModel(config, material_db)
    
    print(f"Target loss: {config.TARGET_LOSS}")
    print(f"Current best: 0.065425 (from previous optimization)")
    print(f"Improvement needed: {0.065425 / config.TARGET_LOSS:.1f}x better")
    
    def objective_function(x):
        """Objective function for TiN property optimization"""
        # x = [n_tin, k_tin] - fine-tunable TiN properties
        return physics_model.calculate_ultra_precise_loss(x)
    
    best_loss = float('inf')
    best_properties = None
    
    # Start from known good TiN properties
    initial_n = 2.0
    initial_k = 1.5
    
    # Strategy 1: Ultra-precise Dual Annealing
    print("\n🔥 Strategy 1: Dual Annealing (Global + Local)")
    
    bounds = [(1.8, 2.5), (1.0, 2.0)]  # Tight bounds around TiN properties
    
    result_da = dual_annealing(
        objective_function,
        bounds,
        maxiter=2000,
        initial_temp=5230,
        restart_temp_ratio=2e-5,
        visit=2.62,
        accept=-5.0,
        maxfun=10000,
        seed=42
    )
    
    if result_da.fun < best_loss:
        best_loss = result_da.fun
        best_properties = result_da.x
    
    print(f"Dual Annealing Result: {result_da.fun:.8f}")
    
    # Strategy 2: Ultra-fine Differential Evolution
    print("\n🧬 Strategy 2: Ultra-fine Differential Evolution")
    
    result_de = differential_evolution(
        objective_function,
        bounds,
        maxiter=3000,
        popsize=100,
        seed=42,
        polish=True,
        atol=1e-12,
        tol=1e-12,
        updating='deferred'
    )
    
    if result_de.fun < best_loss:
        best_loss = result_de.fun
        best_properties = result_de.x
    
    print(f"Differential Evolution Result: {result_de.fun:.8f}")
    
    # Strategy 3: Ultra-precise Local Optimization
    print("\n🎯 Strategy 3: Ultra-precise Local Optimization")
    
    # Multiple starts around promising regions
    start_points = [
        [2.0, 1.5],   # Original TiN values
        [1.95, 1.45], # Slightly lower
        [2.05, 1.55], # Slightly higher
        [2.1, 1.4],   # Mixed variations
        [1.9, 1.6],
        [2.15, 1.35],
        [1.85, 1.65]
    ]
    
    for i, start_point in enumerate(start_points):
        result = minimize(
            objective_function,
            start_point,
            method='L-BFGS-B',
            bounds=bounds,
            options={
                'ftol': 1e-15,
                'gtol': 1e-15,
                'maxiter': 2000,
                'maxfun': 5000
            }
        )
        
        if result.fun < best_loss:
            best_loss = result.fun
            best_properties = result.x
            print(f"  Start {i+1}: New best = {best_loss:.8f}")
            
            # Check if ultra-precise target achieved
            if best_loss < config.TARGET_LOSS:
                print(f"  🎉 ULTRA-PRECISE TARGET ACHIEVED!")
                break
    
    # Strategy 4: Nelder-Mead refinement
    if best_loss > config.TARGET_LOSS:
        print("\n🔬 Strategy 4: Nelder-Mead Ultra-refinement")
        
        result_nm = minimize(
            objective_function,
            best_properties,
            method='Nelder-Mead',
            options={
                'xatol': 1e-12,
                'fatol': 1e-12,
                'maxiter': 5000,
                'adaptive': True
            }
        )
        
        if result_nm.fun < best_loss:
            best_loss = result_nm.fun
            best_properties = result_nm.x
            print(f"Nelder-Mead Result: {best_loss:.8f}")
    
    print(f"\n🎯 ULTRA-PRECISION OPTIMIZATION COMPLETED")
    print(f"Best Loss: {best_loss:.8f}")
    print(f"Target: {config.TARGET_LOSS}")
    print(f"Status: {'✅ ACHIEVED' if best_loss < config.TARGET_LOSS else '❌ NOT ACHIEVED'}")
    
    if best_properties is not None:
        print(f"\n🔬 OPTIMIZED TiN PROPERTIES:")
        print(f"  n (refractive index): {best_properties[0]:.6f}")
        print(f"  k (extinction coeff): {best_properties[1]:.6f}")
    
    return best_properties, best_loss

def create_ultra_precise_visualization(tin_properties, config, loss, total_data_points):
    """Create visualization for ultra-precise results"""
    
    if loss >= config.TARGET_LOSS:
        print(f"\n❌ Loss {loss:.8f} >= {config.TARGET_LOSS}")
        print("Ultra-precise target not achieved")
        return None
    
    print(f"\n🎨 CREATING ULTRA-PRECISE VISUALIZATION")
    print(f"✅ Loss {loss:.8f} < {config.TARGET_LOSS} - Ultra-precise target achieved!")
    
    # Create structure (still 100% TiN_30nm but with optimized properties)
    structure_shape = config.GRID_SIZE
    
    # Create visualization
    fig = plt.figure(figsize=(20, 15))
    
    # Title
    title = f'🎯 ULTRA-PRECISE OPTIMIZATION SUCCESS\n'
    title += f'Loss: {loss:.8f} < {config.TARGET_LOSS} (TARGET ACHIEVED)\n'
    title += f'Optimized 100% TiN Structure with Fine-tuned Properties'
    fig.suptitle(title, fontsize=16, fontweight='bold', color='darkgreen')
    
    # 1. 3D view - all red (optimized TiN)
    ax1 = fig.add_subplot(2, 3, 1, projection='3d')
    
    # Create sample points
    step = 4
    x, y, z = np.meshgrid(
        np.arange(0, structure_shape[0], step),
        np.arange(0, structure_shape[1], step), 
        np.arange(0, structure_shape[2], step),
        indexing='ij'
    )
    
    # All points are optimized TiN (red)
    ax1.scatter(x.flatten(), y.flatten(), z.flatten(), 
               c='red', s=20, alpha=0.8, label='Optimized TiN (100%)')
    
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')
    ax1.set_title('Ultra-Precise TiN Structure\n45nm Thickness')
    ax1.legend()
    
    # 2-4. Cross sections
    for i in range(3):
        ax = fig.add_subplot(2, 3, i+2)
        red_image = np.ones((20, 20, 3))
        red_image[:, :, 0] = 1.0
        red_image[:, :, 1] = 0.0
        red_image[:, :, 2] = 0.0
        ax.imshow(red_image)
        ax.set_title(f'Cross Section {i+1}\nOptimized TiN')
        ax.set_xticks([])
        ax.set_yticks([])
    
    # 5. Properties comparison
    ax5 = fig.add_subplot(2, 3, 5)
    
    original_props = [2.0, 1.5]
    optimized_props = tin_properties
    
    x_pos = [0, 1]
    ax5.bar([0, 1], [original_props[0], optimized_props[0]], 
            color=['lightcoral', 'red'], alpha=0.7, label='n (refractive index)')
    ax5.bar([2, 3], [original_props[1], optimized_props[1]], 
            color=['lightblue', 'blue'], alpha=0.7, label='k (extinction coeff)')
    
    ax5.set_xticks([0.5, 2.5])
    ax5.set_xticklabels(['Original', 'Optimized'])
    ax5.set_ylabel('Property Value')
    ax5.set_title('TiN Property Optimization')
    ax5.legend()
    
    # 6. Summary
    ax6 = fig.add_subplot(2, 3, 6)
    ax6.axis('off')
    
    summary_text = f"""
🎯 ULTRA-PRECISE SUCCESS
====================

LOSS: {loss:.8f} < {config.TARGET_LOSS}
STATUS: TARGET ACHIEVED

OPTIMIZED TiN PROPERTIES:
n: {tin_properties[0]:.6f}
k: {tin_properties[1]:.6f}

IMPROVEMENT:
From: 0.065425
To: {loss:.8f}
Factor: {0.065425/loss:.1f}x better

STRUCTURE:
100% Optimized TiN
45nm thickness
{np.prod(structure_shape):,} voxels

DATA USED:
{total_data_points} experimental points
All angles: 15°, 30°, 45°, 60°

PHYSICS:
Ultra-precise electromagnetic
properties achieved through
fine-tuned TiN parameters
"""
    
    ax6.text(0.05, 0.95, summary_text, transform=ax6.transAxes, 
             fontsize=10, verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgreen', alpha=0.3))
    
    plt.tight_layout()
    
    # Save
    save_path = os.path.join(config.OUTPUT_DIR, 'ULTRA_PRECISE_RESULT_LOSS_001.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ Ultra-precise visualization saved: {save_path}")
    
    return save_path

def main():
    """Main ultra-precision optimization"""
    config = UltraPrecisionConfig()
    
    print("🎯 ULTRA-PRECISION OPTIMIZATION FOR LOSS < 0.01")
    print("Building on 100% TiN_30nm success (loss = 0.065425)")
    print("Target: Achieve loss < 0.01 through fine-tuned TiN properties")
    
    # Load experimental data
    material_db = UltraPrecisionMaterialDatabase(config)
    total_points = len(material_db.get_all_experimental_data())
    
    # Run ultra-precision optimization
    best_properties, final_loss = ultra_precision_optimization(config)
    
    # Create visualization if target achieved
    if final_loss < config.TARGET_LOSS:
        viz_path = create_ultra_precise_visualization(
            best_properties, config, final_loss, total_points
        )
        
        print(f"\n🎉 ULTRA-PRECISE SUCCESS!")
        print(f"✅ Loss: {final_loss:.8f} < {config.TARGET_LOSS}")
        print(f"✅ Improvement: {0.065425/final_loss:.1f}x better than previous")
        print(f"✅ Optimized TiN properties found")
        print(f"✅ Ultra-precise visualization created")
        
    else:
        print(f"\n⚠ Ultra-precise target not achieved")
        print(f"Loss: {final_loss:.8f} >= {config.TARGET_LOSS}")
        print("Consider further optimization strategies")
    
    # Save results
    results = {
        'final_loss': float(final_loss),
        'target_loss': config.TARGET_LOSS,
        'target_achieved': bool(final_loss < config.TARGET_LOSS),
        'optimized_tin_properties': {
            'n': float(best_properties[0]) if best_properties is not None else None,
            'k': float(best_properties[1]) if best_properties is not None else None
        },
        'improvement_factor': 0.065425 / final_loss if final_loss > 0 else None,
        'total_data_points': total_points
    }
    
    with open(os.path.join(config.OUTPUT_DIR, 'ultra_precise_results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nResults saved in: {config.OUTPUT_DIR}")

if __name__ == "__main__":
    main()
