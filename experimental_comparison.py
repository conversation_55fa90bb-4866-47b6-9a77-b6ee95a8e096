#!/usr/bin/env python3
"""
Experimental Data Comparison
============================
Compare our physics simulation with actual experimental measurements
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from physics_validation import MaterialDatabase, TransferMatrixMethod

def load_experimental_data():
    """Load experimental R/T data"""
    exp_data = {}
    
    angles = [15, 30, 45, 60]
    for angle in angles:
        try:
            filename = f"data/RT_{angle}degree_SP.csv"
            if Path(filename).exists():
                data = pd.read_csv(filename)
                # Handle different column names
                if 'Wavelength' in data.columns:
                    data.rename(columns={'Wavelength': 'wavelength'}, inplace=True)
                if 'R' in data.columns and 'T' in data.columns:
                    exp_data[angle] = data
                    print(f"✓ Experimental data loaded for {angle}°: {len(data)} points")
                else:
                    print(f"⚠ Missing R/T columns in {filename}")
            else:
                print(f"⚠ File not found: {filename}")
        except Exception as e:
            print(f"⚠ Error loading {angle}° data: {e}")
    
    return exp_data

def create_simple_structure():
    """Create a simple test structure for validation"""
    # Simple 45nm Al2O3 layer (most reliable material)
    materials = ['Al2O3']
    thicknesses = [45]  # nm
    return materials, thicknesses

def compare_with_experiment():
    """Compare simulation with experimental data"""
    print("\n🔬 Experimental Comparison")
    print("=" * 50)
    
    # Load components
    material_db = MaterialDatabase()
    tmm = TransferMatrixMethod(material_db)
    exp_data = load_experimental_data()
    
    if not exp_data:
        print("❌ No experimental data available for comparison")
        return
    
    # Test structure
    materials, thicknesses = create_simple_structure()
    print(f"\nTest structure: {' / '.join([f'{m}({t}nm)' for m, t in zip(materials, thicknesses)])}")
    
    # Compare at different angles
    for angle in [15, 30, 45, 60]:
        if angle not in exp_data:
            continue
            
        print(f"\n📊 Angle: {angle}°")
        print("-" * 30)
        
        exp_df = exp_data[angle]
        
        # Sample a few wavelengths for comparison
        test_wavelengths = [500, 600, 700, 800, 1000, 1200]
        
        print(f"{'Wavelength':>10} {'R_sim':>8} {'T_sim':>8} {'R_exp':>8} {'T_exp':>8} {'ΔR':>8} {'ΔT':>8}")
        print("-" * 70)
        
        for wl in test_wavelengths:
            # Simulate
            try:
                R_sim, T_sim = tmm.simulate_multilayer(materials, thicknesses, wl, angle)
                
                # Find closest experimental point
                wl_diff = np.abs(exp_df['wavelength'] - wl)
                closest_idx = wl_diff.idxmin()
                
                if wl_diff.iloc[closest_idx] < 50:  # Within 50nm
                    R_exp = exp_df.loc[closest_idx, 'R']
                    T_exp = exp_df.loc[closest_idx, 'T']
                    
                    delta_R = abs(R_sim - R_exp)
                    delta_T = abs(T_sim - T_exp)
                    
                    print(f"{wl:10.0f} {R_sim:8.4f} {T_sim:8.4f} {R_exp:8.4f} {T_exp:8.4f} {delta_R:8.4f} {delta_T:8.4f}")
                else:
                    print(f"{wl:10.0f} {R_sim:8.4f} {T_sim:8.4f} {'N/A':>8} {'N/A':>8} {'N/A':>8} {'N/A':>8}")
                    
            except Exception as e:
                print(f"{wl:10.0f} {'ERROR':>8} {'ERROR':>8} {'N/A':>8} {'N/A':>8} {'N/A':>8} {'N/A':>8}")

def validate_energy_conservation():
    """Test energy conservation across wavelength range"""
    print("\n⚡ Energy Conservation Test")
    print("=" * 50)
    
    material_db = MaterialDatabase()
    tmm = TransferMatrixMethod(material_db)
    
    # Test Al2O3 (most reliable)
    materials = ['Al2O3']
    thicknesses = [45]
    
    wavelengths = np.linspace(400, 1600, 50)
    conservation_errors = []
    
    print(f"Testing {len(wavelengths)} wavelengths...")
    
    for wl in wavelengths:
        try:
            R, T = tmm.simulate_multilayer(materials, thicknesses, wl, 0)
            A = 1 - R - T
            error = abs(R + T + A - 1.0)
            conservation_errors.append(error)
            
            if error > 0.01:  # Flag large errors
                print(f"⚠ Large error at {wl:.0f}nm: R+T+A = {R+T+A:.6f}")
                
        except Exception as e:
            print(f"❌ Error at {wl:.0f}nm: {e}")
            conservation_errors.append(1.0)  # Large error
    
    max_error = max(conservation_errors)
    avg_error = np.mean(conservation_errors)
    
    print(f"\nEnergy Conservation Results:")
    print(f"  Maximum error: {max_error:.8f}")
    print(f"  Average error: {avg_error:.8f}")
    
    if max_error < 1e-10:
        print("  ✅ EXCELLENT: Perfect energy conservation")
    elif max_error < 1e-6:
        print("  ✅ GOOD: Excellent energy conservation")
    elif max_error < 1e-3:
        print("  ⚠ ACCEPTABLE: Good energy conservation")
    else:
        print("  ❌ POOR: Energy conservation issues")

def test_physical_limits():
    """Test that R and T stay within physical bounds"""
    print("\n🔒 Physical Bounds Test")
    print("=" * 50)
    
    material_db = MaterialDatabase()
    tmm = TransferMatrixMethod(material_db)
    
    # Test various structures
    test_cases = [
        (['Al2O3'], [45], "Al2O3 45nm"),
        (['Al2O3'], [10], "Al2O3 10nm (thin)"),
        (['Al2O3'], [100], "Al2O3 100nm (thick)"),
    ]
    
    for materials, thicknesses, description in test_cases:
        print(f"\nTesting: {description}")
        
        violations = 0
        total_tests = 0
        
        for wl in np.linspace(400, 1600, 20):
            for angle in [0, 30, 60]:
                try:
                    R, T = tmm.simulate_multilayer(materials, thicknesses, wl, angle)
                    total_tests += 1
                    
                    # Check bounds
                    if R < 0 or R > 1:
                        print(f"  ❌ R out of bounds: {R:.6f} at {wl:.0f}nm, {angle}°")
                        violations += 1
                    if T < 0 or T > 1:
                        print(f"  ❌ T out of bounds: {T:.6f} at {wl:.0f}nm, {angle}°")
                        violations += 1
                    if R + T > 1.01:  # Allow small numerical error
                        print(f"  ❌ R+T > 1: {R+T:.6f} at {wl:.0f}nm, {angle}°")
                        violations += 1
                        
                except Exception as e:
                    print(f"  ❌ Error at {wl:.0f}nm, {angle}°: {e}")
                    violations += 1
                    total_tests += 1
        
        if violations == 0:
            print(f"  ✅ All {total_tests} tests passed")
        else:
            print(f"  ⚠ {violations}/{total_tests} violations found")

if __name__ == "__main__":
    print("🧪 Experimental Validation Suite")
    print("=" * 60)
    
    # Run validation tests
    compare_with_experiment()
    validate_energy_conservation()
    test_physical_limits()
    
    print("\n" + "=" * 60)
    print("✅ Experimental validation completed!")
    print("\nNext steps:")
    print("1. Fix any identified physics issues")
    print("2. Validate material data quality")
    print("3. Proceed with GAN implementation")
