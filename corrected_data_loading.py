#!/usr/bin/env python3
"""
Corrected Data Loading - Use ALL Experimental Data
=================================================

This script ensures that ALL experimental data points from each CSV file
are loaded and used in the optimization, not just subsampled portions.

Files to load:
- data/RT_15degree_SP.csv (ALL 100 points)
- data/RT_30degree_SP.csv (ALL 100 points) 
- data/RT_45degree_SP.csv (ALL 100 points)
- data/RT_60degree_SP.csv (ALL 100 points)
Total: 400 experimental data points
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.optim as optim
from scipy.optimize import minimize, differential_evolution
import time
import os
import json
import warnings
warnings.filterwarnings('ignore')

class CorrectedConfig:
    """Configuration that uses ALL experimental data"""
    
    EXPERIMENTAL_DATA = {
        15: 'data/RT_15degree_SP.csv',
        30: 'data/RT_30degree_SP.csv',
        45: 'data/RT_45degree_SP.csv',
        60: 'data/RT_60degree_SP.csv'
    }
    
    # Structure parameters
    GRID_SIZE = (32, 16, 32)
    MATERIALS = ['TiN_4nm', 'TiO2', 'Al2O3', 'TiN_30nm']
    TARGET_THICKNESS = 45  # nm
    TARGET_LOSS = 0.01  # Ultra-precise target
    
    # Material properties
    MATERIAL_PROPS = {
        'TiN_4nm': {'n': 2.1, 'k': 1.2},
        'TiO2': {'n': 2.4, 'k': 0.0},
        'Al2O3': {'n': 1.77, 'k': 0.0},
        'TiN_30nm': {'n': 2.0, 'k': 1.5}
    }
    
    OUTPUT_DIR = 'results_corrected_data/'

os.makedirs(CorrectedConfig.OUTPUT_DIR, exist_ok=True)

class CorrectedDataLoader:
    """Data loader that uses ALL experimental data points"""
    
    def __init__(self, config):
        self.config = config
        self.experimental_data = {}
        self.all_data_points = []
        self._load_all_experimental_data()
        
    def _load_all_experimental_data(self):
        """Load ALL experimental data - no subsampling or limitations"""
        print("LOADING ALL EXPERIMENTAL DATA")
        print("="*50)
        
        total_points = 0
        for angle, file_path in self.config.EXPERIMENTAL_DATA.items():
            if os.path.exists(file_path):
                try:
                    # Load the complete CSV file
                    data = pd.read_csv(file_path)
                    
                    print(f"Raw data from {file_path}:")
                    print(f"  Total rows: {len(data)}")
                    print(f"  Columns: {list(data.columns)}")
                    
                    # Only filter for physically valid values - NO SUBSAMPLING
                    valid_data = data[(data['R'] >= 0) & (data['R'] <= 1) & 
                                     (data['T'] >= 0) & (data['T'] <= 1)]
                    
                    # Store ALL valid data points
                    self.experimental_data[angle] = valid_data
                    
                    # Add to comprehensive list for optimization
                    for _, row in valid_data.iterrows():
                        self.all_data_points.append({
                            'angle': angle,
                            'wavelength': row['wavelength'],
                            'R_exp': row['R'],
                            'T_exp': row['T']
                        })
                    
                    total_points += len(valid_data)
                    print(f"  Loaded ALL {len(valid_data)} valid points for {angle} degrees")
                    print(f"  Wavelength range: {valid_data['wavelength'].min():.0f} - {valid_data['wavelength'].max():.0f} nm")
                    print(f"  R range: {valid_data['R'].min():.3f} - {valid_data['R'].max():.3f}")
                    print(f"  T range: {valid_data['T'].min():.3f} - {valid_data['T'].max():.3f}")
                    print()
                    
                except Exception as e:
                    print(f"ERROR loading {file_path}: {e}")
            else:
                print(f"FILE NOT FOUND: {file_path}")
        
        print(f"TOTAL EXPERIMENTAL DATA POINTS: {total_points}")
        print(f"ALL DATA LOADED - NO SUBSAMPLING APPLIED")
        
        if total_points != 400:
            print(f"WARNING: Expected 400 points (100 per file), got {total_points}")
        
    def get_all_data_points(self):
        """Get all experimental data points"""
        return self.all_data_points
    
    def verify_data_completeness(self):
        """Verify that all data has been loaded correctly"""
        print("\nDATA COMPLETENESS VERIFICATION")
        print("="*40)
        
        expected_per_file = 100
        expected_total = 400
        
        for angle in [15, 30, 45, 60]:
            if angle in self.experimental_data:
                actual_count = len(self.experimental_data[angle])
                status = "OK" if actual_count == expected_per_file else "ISSUE"
                print(f"{angle} degrees: {actual_count}/{expected_per_file} points - {status}")
            else:
                print(f"{angle} degrees: NO DATA LOADED - ISSUE")
        
        actual_total = len(self.all_data_points)
        status = "OK" if actual_total == expected_total else "ISSUE"
        print(f"\nTotal: {actual_total}/{expected_total} points - {status}")
        
        return actual_total == expected_total

class CorrectedPhysicsModel:
    """Physics model that uses ALL experimental data"""
    
    def __init__(self, config, data_loader):
        self.config = config
        self.data_loader = data_loader
        self.experimental_data = data_loader.get_all_data_points()
        print(f"Physics model initialized with ALL {len(self.experimental_data)} data points")
        
    def calculate_rt_tmm(self, material_fractions, wavelength_nm, angle):
        """Calculate R,T using Transfer Matrix Method"""
        wavelength_um = wavelength_nm * 1e-3
        
        # Get material properties
        n_values = []
        k_values = []
        for material in self.config.MATERIALS:
            props = self.config.MATERIAL_PROPS[material]
            n_values.append(props['n'])
            k_values.append(props['k'])
        
        # Effective refractive index (weighted average)
        n_eff = np.sum([f * n for f, n in zip(material_fractions, n_values)])
        k_eff = np.sum([f * k for f, k in zip(material_fractions, k_values)])
        
        # Ensure physical bounds
        n_eff = np.clip(n_eff, 1.0, 4.0)
        k_eff = np.clip(k_eff, 0.0, 3.0)
        
        # Angle-dependent calculation
        n_air = 1.0
        angle_rad = np.radians(angle)
        cos_theta_i = np.cos(angle_rad)
        sin_theta_i = np.sin(angle_rad)
        
        # Snell's law
        sin_theta_t = (n_air * sin_theta_i) / n_eff
        
        if sin_theta_t > 1.0:
            return 1.0, 0.0  # Total internal reflection
            
        cos_theta_t = np.sqrt(1 - sin_theta_t**2)
        
        # Complex refractive index
        n_complex = complex(n_eff, k_eff)
        
        # Fresnel coefficients
        r_s = (n_air * cos_theta_i - n_complex * cos_theta_t) / (n_air * cos_theta_i + n_complex * cos_theta_t)
        r_p = (n_complex * cos_theta_i - n_air * cos_theta_t) / (n_complex * cos_theta_i + n_air * cos_theta_t)
        
        # Average for unpolarized light
        R = 0.5 * (abs(r_s)**2 + abs(r_p)**2)
        
        # Transmission with absorption
        thickness_um = self.config.TARGET_THICKNESS * 1e-3
        alpha = 4 * np.pi * k_eff / wavelength_um
        path_length = thickness_um / cos_theta_t
        absorption = np.exp(-alpha * path_length)
        
        # Transmission coefficient
        t_s = (2 * n_air * cos_theta_i) / (n_air * cos_theta_i + n_complex * cos_theta_t)
        t_p = (2 * n_air * cos_theta_i) / (n_complex * cos_theta_i + n_air * cos_theta_t)
        
        T_fresnel = 0.5 * (abs(t_s)**2 + abs(t_p)**2) * (n_eff * cos_theta_t) / (n_air * cos_theta_i)
        T = T_fresnel * absorption
        
        # Physical bounds
        R = np.clip(R, 0.0, 1.0)
        T = np.clip(T, 0.0, 1.0 - R)
        
        return R, T
    
    def calculate_loss_all_data(self, material_fractions):
        """Calculate loss using ALL experimental data points"""
        total_loss = 0.0
        valid_points = 0
        
        # Use ALL experimental data points - no sampling
        for data_point in self.experimental_data:
            try:
                R_sim, T_sim = self.calculate_rt_tmm(
                    material_fractions,
                    data_point['wavelength'],
                    data_point['angle']
                )
                
                R_exp = data_point['R_exp']
                T_exp = data_point['T_exp']
                
                # Multi-component loss calculation
                eps = 1e-10
                
                # Relative errors (primary)
                loss_R_rel = abs((R_sim - R_exp) / (R_exp + eps))
                loss_T_rel = abs((T_sim - T_exp) / (T_exp + eps))
                
                # Absolute errors (secondary)
                loss_R_abs = abs(R_sim - R_exp)
                loss_T_abs = abs(T_sim - T_exp)
                
                # Combined loss
                point_loss = 0.6 * (loss_R_rel + loss_T_rel) + 0.4 * (loss_R_abs + loss_T_abs)
                
                total_loss += point_loss
                valid_points += 1
                
            except:
                continue
        
        if valid_points > 0:
            avg_loss = total_loss / valid_points
            return avg_loss
        else:
            return 1000.0

def run_corrected_optimization(config):
    """Run optimization using ALL experimental data"""
    print("\nCORRECTED OPTIMIZATION - USING ALL DATA")
    print("="*60)
    
    # Load ALL data
    data_loader = CorrectedDataLoader(config)
    
    # Verify data completeness
    data_complete = data_loader.verify_data_completeness()
    if not data_complete:
        print("WARNING: Data loading incomplete!")
        return None, None
    
    # Initialize physics model
    physics_model = CorrectedPhysicsModel(config, data_loader)
    
    print(f"\nOptimization using ALL {len(physics_model.experimental_data)} data points")
    print(f"Target loss: {config.TARGET_LOSS}")
    
    def objective_function(x):
        """Objective function using ALL data"""
        # Normalize to ensure sum = 1
        x_norm = x / np.sum(x)
        return physics_model.calculate_loss_all_data(x_norm)
    
    # Optimization bounds
    bounds = [(0.001, 0.999) for _ in range(4)]
    
    best_loss = float('inf')
    best_fractions = None
    
    # Strategy 1: Differential Evolution with ALL data
    print("\nStrategy 1: Differential Evolution (using ALL data)")
    
    start_time = time.time()
    result_de = differential_evolution(
        objective_function,
        bounds,
        maxiter=2000,
        popsize=100,
        seed=42,
        polish=True,
        atol=1e-12,
        tol=1e-12
    )
    de_time = time.time() - start_time
    
    if result_de.fun < best_loss:
        best_loss = result_de.fun
        best_fractions = result_de.x
    
    print(f"Differential Evolution Result: {result_de.fun:.8f} (time: {de_time:.1f}s)")
    
    # Strategy 2: Multiple local optimizations
    print("\nStrategy 2: Multiple Local Optimizations (using ALL data)")
    
    start_points = [
        [0.25, 0.25, 0.25, 0.25],  # Equal fractions
        [0.7, 0.1, 0.1, 0.1],     # TiN_4nm dominated
        [0.1, 0.7, 0.1, 0.1],     # TiO2 dominated
        [0.1, 0.1, 0.7, 0.1],     # Al2O3 dominated
        [0.1, 0.1, 0.1, 0.7],     # TiN_30nm dominated
    ]
    
    for i, start_point in enumerate(start_points):
        result = minimize(
            objective_function,
            start_point,
            method='L-BFGS-B',
            bounds=bounds,
            options={'ftol': 1e-15, 'gtol': 1e-15, 'maxiter': 2000}
        )
        
        if result.fun < best_loss:
            best_loss = result.fun
            best_fractions = result.x
            print(f"  Start {i+1}: New best = {best_loss:.8f}")
    
    # Normalize final result
    if best_fractions is not None:
        best_fractions = best_fractions / np.sum(best_fractions)
    
    print(f"\nCORRECTED OPTIMIZATION COMPLETED")
    print(f"Best Loss: {best_loss:.8f}")
    print(f"Target: {config.TARGET_LOSS}")
    print(f"Status: {'ACHIEVED' if best_loss < config.TARGET_LOSS else 'NOT ACHIEVED'}")
    
    if best_fractions is not None:
        print(f"\nOptimal Material Fractions (using ALL data):")
        for i, material in enumerate(config.MATERIALS):
            print(f"  {material}: {best_fractions[i]:.4f} ({best_fractions[i]*100:.1f}%)")
    
    return best_fractions, best_loss

def main():
    """Main function"""
    config = CorrectedConfig()
    
    print("CORRECTED DATA LOADING AND OPTIMIZATION")
    print("="*60)
    print("This ensures ALL experimental data points are used")
    print("No subsampling, no limitations - complete dataset")
    
    # Run corrected optimization
    best_fractions, final_loss = run_corrected_optimization(config)
    
    # Save results
    if best_fractions is not None:
        results = {
            'final_loss': float(final_loss),
            'target_loss': config.TARGET_LOSS,
            'target_achieved': bool(final_loss < config.TARGET_LOSS),
            'material_fractions': {
                config.MATERIALS[i]: float(best_fractions[i]) 
                for i in range(len(config.MATERIALS))
            },
            'total_data_points_used': 400,
            'data_loading_method': 'ALL_DATA_NO_SUBSAMPLING',
            'verification': 'COMPLETE_DATASET_USED'
        }
        
        with open(os.path.join(config.OUTPUT_DIR, 'corrected_optimization_results.json'), 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\nResults saved to: {config.OUTPUT_DIR}")
        print(f"Verification: ALL 400 experimental data points were used")
        
    else:
        print("Optimization failed - no valid results")

if __name__ == "__main__":
    main()
