#!/usr/bin/env python3
"""
MEEP Functionality Test
=======================
Comprehensive test to ensure MEEP is fully operational
"""

import numpy as np
import meep as mp

def test_meep_basic_functionality():
    """Test basic MEEP functionality"""
    print("🔧 Testing MEEP Basic Functionality")
    print("=" * 50)
    
    try:
        # Test 1: Create basic materials
        print("Test 1: Material creation")
        air = mp.Medium()
        glass = mp.Medium(epsilon=2.25)  # n = 1.5
        metal = mp.Medium(epsilon=1.0, D_conductivity=1e6)
        print("  ✓ Materials created successfully")
        
        # Test 2: Create geometry
        print("Test 2: Geometry creation")
        geometry = [
            mp.Block(center=mp.Vector3(0, 0, 0),
                    size=mp.Vector3(1, 1, 0.1),
                    material=glass)
        ]
        print("  ✓ Geometry created successfully")
        
        # Test 3: Create simulation cell
        print("Test 3: Simulation cell")
        cell = mp.Vector3(2, 2, 2)
        pml_layers = [mp.PML(thickness=0.5)]
        print("  ✓ Cell and PML created successfully")
        
        # Test 4: Create sources
        print("Test 4: Source creation")
        sources = [mp.Source(
            mp.ContinuousSource(frequency=1.0),
            component=mp.Ex,
            center=mp.Vector3(0, 0, -0.5)
        )]
        print("  ✓ Sources created successfully")
        
        # Test 5: Create simulation object
        print("Test 5: Simulation object")
        sim = mp.Simulation(
            cell_size=cell,
            boundary_layers=pml_layers,
            geometry=geometry,
            sources=sources,
            resolution=10
        )
        print("  ✓ Simulation object created successfully")
        
        # Test 6: Add flux monitor
        print("Test 6: Flux monitor")
        flux_region = mp.FluxRegion(center=mp.Vector3(0, 0, 0.5))
        flux = sim.add_flux(1.0, 0, 1, flux_region)
        print("  ✓ Flux monitor added successfully")
        
        print("\n🎉 ALL MEEP FUNCTIONALITY TESTS PASSED")
        return True
        
    except Exception as e:
        print(f"❌ MEEP functionality test failed: {e}")
        return False

def test_meep_simple_simulation():
    """Test a simple MEEP simulation"""
    print("\n🔬 Testing MEEP Simple Simulation")
    print("=" * 50)
    
    try:
        # Simple 1D simulation
        cell = mp.Vector3(0, 0, 10)
        
        # Glass slab in the middle
        geometry = [mp.Block(
            center=mp.Vector3(0, 0, 0),
            size=mp.Vector3(mp.inf, mp.inf, 1),
            material=mp.Medium(epsilon=2.25)
        )]
        
        # Source
        sources = [mp.Source(
            mp.GaussianSource(frequency=1.0, fwidth=0.1),
            component=mp.Ex,
            center=mp.Vector3(0, 0, -2)
        )]
        
        # PML
        pml_layers = [mp.PML(thickness=1.0)]
        
        # Simulation
        sim = mp.Simulation(
            cell_size=cell,
            boundary_layers=pml_layers,
            geometry=geometry,
            sources=sources,
            resolution=20
        )
        
        # Flux monitors
        refl = sim.add_flux(1.0, 0, 1, 
                           mp.FluxRegion(center=mp.Vector3(0, 0, -1)))
        trans = sim.add_flux(1.0, 0, 1,
                            mp.FluxRegion(center=mp.Vector3(0, 0, 1)))
        
        # Run simulation
        print("  Running short MEEP simulation...")
        sim.run(until=10)
        
        # Get results
        refl_flux = mp.get_fluxes(refl)[0]
        trans_flux = mp.get_fluxes(trans)[0]
        
        print(f"  ✓ Simulation completed")
        print(f"  ✓ Reflection flux: {refl_flux:.6f}")
        print(f"  ✓ Transmission flux: {trans_flux:.6f}")
        
        # Basic physics check
        if abs(refl_flux) > 0 and abs(trans_flux) > 0:
            print("  ✓ Physics results reasonable")
        else:
            print("  ⚠ Physics results may need verification")
        
        print("\n🎉 MEEP SIMULATION TEST PASSED")
        return True
        
    except Exception as e:
        print(f"❌ MEEP simulation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_meep_material_properties():
    """Test MEEP material property handling"""
    print("\n🧪 Testing MEEP Material Properties")
    print("=" * 50)
    
    try:
        # Test different material types
        materials = {
            'air': mp.Medium(),
            'glass': mp.Medium(epsilon=2.25),
            'lossy_glass': mp.Medium(epsilon=2.25, D_conductivity=0.01),
            'metal': mp.Medium(epsilon=1.0, D_conductivity=1e6),
            'complex_material': mp.Medium(epsilon=4.0)  # Complex epsilon not directly supported
        }
        
        for name, material in materials.items():
            print(f"  ✓ {name}: {type(material).__name__}")
        
        # Test frequency-dependent materials
        print("  Testing frequency-dependent materials...")
        
        # Lorentzian material
        lorentz_material = mp.Medium(
            epsilon=1.0,
            E_susceptibilities=[
                mp.LorentzianSusceptibility(
                    frequency=1.0, gamma=0.1, sigma=0.5
                )
            ]
        )
        print("  ✓ Lorentzian material created")
        
        print("\n🎉 MEEP MATERIAL PROPERTY TESTS PASSED")
        return True
        
    except Exception as e:
        print(f"❌ MEEP material property test failed: {e}")
        return False

def main():
    """Run comprehensive MEEP functionality tests"""
    print("🔧 MEEP CONFIGURATION & FUNCTIONALITY VERIFICATION")
    print("=" * 80)
    print(f"MEEP Version: {mp.__version__}")
    print(f"Available attributes: {len([attr for attr in dir(mp) if not attr.startswith('_')])}")
    
    # Run all tests
    tests = [
        test_meep_basic_functionality,
        test_meep_material_properties,
        test_meep_simple_simulation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 80)
    print("MEEP FUNCTIONALITY TEST SUMMARY")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 MEEP IS FULLY OPERATIONAL AND READY FOR USE")
        print("✅ All required functionality verified")
        print("✅ Ready for electromagnetic simulation validation")
    else:
        print("⚠ Some MEEP functionality issues detected")
        print("🔧 Additional configuration may be required")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🚀 MEEP is ready for the validation protocol!")
    else:
        print("\n❌ MEEP requires additional configuration")
