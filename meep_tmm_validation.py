#!/usr/bin/env python3
"""
MEEP-TMM Validation Protocol
============================
Mandatory validation using MEEP for FDTD simulations
Exact structure: 300nm PML + 500nm air + 45nm material + 500nm air + 300nm PML
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path

# Import MEEP (mandatory requirement)
try:
    import meep as mp
    print("✓ MEEP/PyMEEP successfully imported")
    MEEP_AVAILABLE = True
except ImportError as e:
    print(f"❌ CRITICAL ERROR: MEEP not available - {e}")
    print("❌ VALIDATION CANNOT PROCEED WITHOUT MEEP")
    MEEP_AVAILABLE = False
    exit(1)

class MaterialDatabase:
    """Load material data with exact specifications"""
    
    def __init__(self):
        self.materials = {}
        self.load_materials()
    
    def load_materials(self):
        """Load materials with mandatory TiN-4nm data"""
        
        # Load Al2O3 reference data
        try:
            al2o3_file = Path("data/Al2O3.txt")
            if al2o3_file.exists():
                data = pd.read_csv(al2o3_file, sep='\s+', header=None, names=['wavelength', 'n', 'k'])
                self.materials['Al2O3'] = data
                print(f"✓ Al2O3 loaded: {len(data)} points ({data['wavelength'].min():.0f}-{data['wavelength'].max():.0f}nm)")
            else:
                raise FileNotFoundError("Al2O3.txt required for validation")
        except Exception as e:
            print(f"❌ CRITICAL: Al2O3 loading failed - {e}")
            exit(1)
        
        # Load mandatory TiN-4nm data
        try:
            tin_file = Path("data/TiN_4nm.csv")
            if tin_file.exists():
                data = pd.read_csv(tin_file)
                # Ensure proper column names and units
                if 'wavelength' not in data.columns and 'Wavelength' in data.columns:
                    data.rename(columns={'Wavelength': 'wavelength'}, inplace=True)
                
                # Convert units if needed (ensure nm)
                if data['wavelength'].max() < 10:  # If in micrometers
                    data['wavelength'] = data['wavelength'] * 1000
                
                # Ensure numeric types
                for col in ['wavelength', 'n', 'k']:
                    if col in data.columns:
                        data[col] = pd.to_numeric(data[col], errors='coerce')
                
                self.materials['TiN_4nm'] = data
                print(f"✓ TiN-4nm loaded: {len(data)} points ({data['wavelength'].min():.0f}-{data['wavelength'].max():.0f}nm)")
                print(f"  n range: {data['n'].min():.3f} - {data['n'].max():.3f}")
                print(f"  k range: {data['k'].min():.3f} - {data['k'].max():.3f}")
            else:
                raise FileNotFoundError("TiN_4nm.csv is MANDATORY for validation")
        except Exception as e:
            print(f"❌ CRITICAL: TiN-4nm loading failed - {e}")
            exit(1)
    
    def get_refractive_index(self, material, wavelength_nm):
        """Get complex refractive index with validation"""
        if material not in self.materials:
            raise ValueError(f"Material {material} not found in database")
        
        data = self.materials[material]
        
        # Interpolate n and k
        n = np.interp(wavelength_nm, data['wavelength'], data['n'])
        k = np.interp(wavelength_nm, data['wavelength'], data['k'])
        
        # Validation checks
        if np.isnan(n) or np.isnan(k):
            raise ValueError(f"Invalid n/k values for {material} at {wavelength_nm}nm")
        
        return n + 1j * k

class MEEPSimulator:
    """MEEP FDTD simulator with exact structure specifications"""
    
    def __init__(self, material_db):
        self.material_db = material_db
        
        # Exact structure dimensions (in micrometers for MEEP)
        self.pml_thickness = 0.3      # 300nm PML
        self.air_gap = 0.5           # 500nm air gaps
        self.material_thickness = 0.045  # 45nm material layer
        self.lateral_size = 1.0      # 1000nm lateral dimensions
        
        # Total simulation size
        self.total_size = 2 * self.pml_thickness + 2 * self.air_gap + self.material_thickness
        
        print(f"✓ MEEP structure configured:")
        print(f"  PML layers: {self.pml_thickness*1000:.0f}nm each")
        print(f"  Air gaps: {self.air_gap*1000:.0f}nm each")
        print(f"  Material layer: {self.material_thickness*1000:.0f}nm")
        print(f"  Total size: {self.total_size*1000:.0f}nm")
    
    def create_meep_material(self, material_name, wavelength_nm):
        """Create MEEP material with proper error handling"""
        try:
            n_complex = self.material_db.get_refractive_index(material_name, wavelength_nm)
            epsilon = n_complex ** 2
            
            # Create MEEP medium
            if n_complex.imag == 0:
                # Lossless material
                return mp.Medium(epsilon=epsilon.real)
            else:
                # Lossy material - use conductivity
                # Convert to MEEP units (1/μm)
                conductivity = 2 * np.pi * n_complex.imag * 1000 / wavelength_nm  # Convert nm to μm
                return mp.Medium(epsilon=epsilon.real, conductivity=conductivity)
                
        except Exception as e:
            print(f"❌ MEEP material creation failed for {material_name}: {e}")
            raise
    
    def simulate_structure(self, material_name, wavelength_nm, angle_deg=0):
        """
        MEEP simulation with exact validation structure
        
        Structure (top to bottom):
        - 300nm PML
        - 500nm air (reflection detector + source)
        - 45nm material layer
        - 500nm air (transmission detector)
        - 300nm PML
        """
        try:
            print(f"\n🔬 MEEP Simulation: {material_name} @ {wavelength_nm}nm, {angle_deg}°")
            
            # Convert to MEEP units
            wavelength_um = wavelength_nm / 1000.0
            frequency = 1.0 / wavelength_um
            
            # Create material
            material = self.create_meep_material(material_name, wavelength_nm)
            
            # Define geometry
            geometry = []
            
            # Material layer at center
            material_layer = mp.Block(
                center=mp.Vector3(0, 0, 0),
                size=mp.Vector3(self.lateral_size, self.lateral_size, self.material_thickness),
                material=material
            )
            geometry.append(material_layer)
            
            # Simulation cell
            cell = mp.Vector3(self.lateral_size, self.lateral_size, self.total_size)
            
            # PML boundaries
            pml_layers = [mp.PML(thickness=self.pml_thickness)]
            
            # Source position (in first air gap)
            source_z = -self.material_thickness/2 - self.air_gap/2
            
            # Handle angled incidence
            if angle_deg == 0:
                # Normal incidence
                sources = [mp.Source(
                    mp.ContinuousSource(frequency=frequency),
                    component=mp.Ex,
                    center=mp.Vector3(0, 0, source_z)
                )]
            else:
                # Angled incidence - use plane wave
                theta = np.radians(angle_deg)
                k_vector = mp.Vector3(np.sin(theta), 0, np.cos(theta))
                sources = [mp.Source(
                    mp.ContinuousSource(frequency=frequency),
                    component=mp.Ex,
                    center=mp.Vector3(0, 0, source_z),
                    k_point=k_vector
                )]
            
            # Create simulation
            sim = mp.Simulation(
                cell_size=cell,
                boundary_layers=pml_layers,
                geometry=geometry,
                sources=sources,
                resolution=20,  # 20 points per micrometer (50nm resolution)
                force_complex_fields=True
            )
            
            # Flux monitors
            # Reflection monitor (in first air gap)
            refl_z = source_z + self.air_gap/4
            refl_region = mp.FluxRegion(
                center=mp.Vector3(0, 0, refl_z),
                size=mp.Vector3(self.lateral_size*0.8, self.lateral_size*0.8, 0)
            )
            refl_flux = sim.add_flux(frequency, 0, 1, refl_region)
            
            # Transmission monitor (in second air gap)
            trans_z = self.material_thickness/2 + self.air_gap/2
            trans_region = mp.FluxRegion(
                center=mp.Vector3(0, 0, trans_z),
                size=mp.Vector3(self.lateral_size*0.8, self.lateral_size*0.8, 0)
            )
            trans_flux = sim.add_flux(frequency, 0, 1, trans_region)
            
            # Run simulation
            print("  Running MEEP simulation...")
            sim.run(until_after_sources=mp.stop_when_fields_decayed(
                50, mp.Ex, mp.Vector3(0, 0, trans_z), 1e-9
            ))
            
            # Get flux values
            refl_data = mp.get_fluxes(refl_flux)
            trans_data = mp.get_fluxes(trans_flux)
            
            if len(refl_data) == 0 or len(trans_data) == 0:
                raise ValueError("No flux data obtained from MEEP")
            
            # Calculate R and T
            incident_flux = abs(refl_data[0]) + abs(trans_data[0])  # Approximate
            R = abs(refl_data[0]) / incident_flux if incident_flux > 0 else 0
            T = abs(trans_data[0]) / incident_flux if incident_flux > 0 else 0
            
            # Ensure physical bounds
            R = max(0, min(1, R))
            T = max(0, min(1, T))
            
            print(f"  ✓ MEEP Results: R = {R:.6f}, T = {T:.6f}")
            
            return R, T
            
        except Exception as e:
            print(f"❌ MEEP simulation failed: {e}")
            raise

class TMMSimulator:
    """Transfer Matrix Method for comparison"""
    
    def __init__(self, material_db):
        self.material_db = material_db
    
    def simulate_structure(self, material_name, wavelength_nm, angle_deg=0):
        """TMM simulation for single layer"""
        try:
            print(f"🧮 TMM Simulation: {material_name} @ {wavelength_nm}nm, {angle_deg}°")
            
            # Get material properties
            n_complex = self.material_db.get_refractive_index(material_name, wavelength_nm)
            
            # Single layer TMM calculation
            n_air = 1.0 + 0j
            n_material = n_complex
            
            # Layer thickness in nm
            thickness_nm = 45.0
            
            # Angle conversion
            theta_0 = np.radians(angle_deg)
            
            # Snell's law
            sin_theta_material = (n_air.real * np.sin(theta_0)) / n_material.real
            if abs(sin_theta_material) > 1:
                # Total internal reflection
                R, T = 1.0, 0.0
            else:
                theta_material = np.arcsin(sin_theta_material)
                
                # Wave vector
                k0 = 2 * np.pi / wavelength_nm
                beta = k0 * n_material * thickness_nm * np.cos(theta_material)
                
                # Transfer matrix elements
                cos_beta = np.cos(beta)
                sin_beta = np.sin(beta)
                
                # Admittances
                Y_air = n_air * np.cos(theta_0)
                Y_material = n_material * np.cos(theta_material)
                
                # Matrix elements
                m11 = cos_beta
                m12 = 1j * sin_beta / Y_material
                m21 = 1j * Y_material * sin_beta
                m22 = cos_beta
                
                # Reflection and transmission coefficients
                r = (Y_air * m11 + Y_air**2 * m12 - m21 - Y_air * m22) / \
                    (Y_air * m11 + Y_air**2 * m12 + m21 + Y_air * m22)
                
                t = 2 * Y_air / (Y_air * m11 + Y_air**2 * m12 + m21 + Y_air * m22)
                
                # Power coefficients
                R = abs(r)**2
                T = (Y_air.real / Y_air.real) * abs(t)**2
            
            print(f"  ✓ TMM Results: R = {R:.6f}, T = {T:.6f}")
            
            return R, T
            
        except Exception as e:
            print(f"❌ TMM simulation failed: {e}")
            raise

class ValidationProtocol:
    """MEEP-TMM Validation Protocol Implementation"""

    def __init__(self):
        self.material_db = MaterialDatabase()
        self.meep_sim = MEEPSimulator(self.material_db)
        self.tmm_sim = TMMSimulator(self.material_db)

        # Validation thresholds
        self.thresholds = {
            'Al2O3': {'R': 0.01, 'T': 0.01},    # ≤1% deviation
            'TiN_4nm': {'R': 0.02, 'T': 0.02}   # ≤2% deviation
        }

        print("✓ Validation Protocol initialized")

    def calculate_deviation(self, meep_val, tmm_val):
        """Calculate percentage deviation"""
        if tmm_val == 0:
            return abs(meep_val - tmm_val)
        return abs(meep_val - tmm_val) / abs(tmm_val)

    def validate_material(self, material_name, wavelength_nm, angle_deg=0):
        """Validate single material at specific conditions"""
        print(f"\n{'='*60}")
        print(f"VALIDATING: {material_name} @ {wavelength_nm}nm, {angle_deg}°")
        print(f"{'='*60}")

        try:
            # Run MEEP simulation
            R_meep, T_meep = self.meep_sim.simulate_structure(material_name, wavelength_nm, angle_deg)

            # Run TMM simulation
            R_tmm, T_tmm = self.tmm_sim.simulate_structure(material_name, wavelength_nm, angle_deg)

            # Calculate deviations
            R_deviation = self.calculate_deviation(R_meep, R_tmm)
            T_deviation = self.calculate_deviation(T_meep, T_tmm)

            # Get thresholds
            thresholds = self.thresholds.get(material_name, {'R': 0.02, 'T': 0.02})

            # Energy conservation check
            energy_meep = R_meep + T_meep
            energy_tmm = R_tmm + T_tmm

            print(f"\n📊 COMPARISON RESULTS:")
            print(f"{'Method':<8} {'R':<10} {'T':<10} {'R+T':<10}")
            print(f"{'-'*40}")
            print(f"{'MEEP':<8} {R_meep:<10.6f} {T_meep:<10.6f} {energy_meep:<10.6f}")
            print(f"{'TMM':<8} {R_tmm:<10.6f} {T_tmm:<10.6f} {energy_tmm:<10.6f}")
            print(f"{'-'*40}")
            print(f"{'Deviation':<8} {R_deviation:<10.4%} {T_deviation:<10.4%}")
            print(f"{'Threshold':<8} {thresholds['R']:<10.4%} {thresholds['T']:<10.4%}")

            # Validation checks
            R_pass = R_deviation <= thresholds['R']
            T_pass = T_deviation <= thresholds['T']
            energy_pass = energy_meep <= 1.01 and energy_tmm <= 1.01

            print(f"\n🔍 VALIDATION STATUS:")
            print(f"  R deviation: {'✓ PASS' if R_pass else '❌ FAIL'} ({R_deviation:.4%} vs {thresholds['R']:.1%})")
            print(f"  T deviation: {'✓ PASS' if T_pass else '❌ FAIL'} ({T_deviation:.4%} vs {thresholds['T']:.1%})")
            print(f"  Energy conservation: {'✓ PASS' if energy_pass else '❌ FAIL'}")

            overall_pass = R_pass and T_pass and energy_pass

            if overall_pass:
                print(f"\n🎉 VALIDATION PASSED for {material_name}")
            else:
                print(f"\n💥 VALIDATION FAILED for {material_name}")
                print("🔧 DIAGNOSIS REQUIRED:")
                if not R_pass:
                    print(f"  - R deviation too high: {R_deviation:.4%} > {thresholds['R']:.1%}")
                if not T_pass:
                    print(f"  - T deviation too high: {T_deviation:.4%} > {thresholds['T']:.1%}")
                if not energy_pass:
                    print(f"  - Energy conservation violated")

            return {
                'material': material_name,
                'wavelength': wavelength_nm,
                'angle': angle_deg,
                'R_meep': R_meep,
                'T_meep': T_meep,
                'R_tmm': R_tmm,
                'T_tmm': T_tmm,
                'R_deviation': R_deviation,
                'T_deviation': T_deviation,
                'R_pass': R_pass,
                'T_pass': T_pass,
                'energy_pass': energy_pass,
                'overall_pass': overall_pass
            }

        except Exception as e:
            print(f"❌ VALIDATION ERROR: {e}")
            return None

    def run_full_validation(self):
        """Execute complete validation protocol"""
        print("\n" + "="*80)
        print("ELECTROMAGNETIC SIMULATION VALIDATION PROTOCOL")
        print("="*80)
        print("Mandatory MEEP validation with exact structure specifications")
        print("Structure: 300nm PML + 500nm air + 45nm material + 500nm air + 300nm PML")

        results = []

        # Phase 1: Al2O3 Benchmark
        print(f"\n🔬 PHASE 1: Al2O3 BENCHMARK")
        print(f"{'='*50}")

        al2o3_result = self.validate_material('Al2O3', 800, 0)  # 800nm, normal incidence
        if al2o3_result:
            results.append(al2o3_result)

            if al2o3_result['overall_pass']:
                print(f"✅ Al2O3 benchmark PASSED - Proceeding to TiN validation")
            else:
                print(f"❌ Al2O3 benchmark FAILED - Cannot proceed to TiN")
                print(f"🔧 Required: Debug material parameters, boundary conditions, PML settings")
                return results

        # Phase 2: TiN-4nm Validation
        print(f"\n🔬 PHASE 2: TiN-4nm VALIDATION")
        print(f"{'='*50}")

        tin_result = self.validate_material('TiN_4nm', 800, 0)  # 800nm, normal incidence
        if tin_result:
            results.append(tin_result)

            if tin_result['overall_pass']:
                print(f"✅ TiN-4nm validation PASSED")
            else:
                print(f"❌ TiN-4nm validation FAILED")
                print(f"🔧 Required: Debug material data, PML settings")

        # Additional angle validation
        print(f"\n🔬 ANGLE CONSISTENCY CHECK")
        print(f"{'='*50}")

        for angle in [15, 30, 45]:
            angle_result = self.validate_material('Al2O3', 800, angle)
            if angle_result:
                results.append(angle_result)

        # Summary
        self.print_validation_summary(results)

        return results

    def print_validation_summary(self, results):
        """Print comprehensive validation summary"""
        print(f"\n" + "="*80)
        print("VALIDATION SUMMARY")
        print("="*80)

        if not results:
            print("❌ NO VALIDATION RESULTS AVAILABLE")
            return

        passed = sum(1 for r in results if r['overall_pass'])
        total = len(results)

        print(f"Overall Success Rate: {passed}/{total} ({passed/total*100:.1f}%)")

        print(f"\n📊 DETAILED RESULTS:")
        print(f"{'Material':<12} {'λ(nm)':<8} {'Angle':<8} {'R_dev':<8} {'T_dev':<8} {'Status':<8}")
        print(f"{'-'*60}")

        for r in results:
            status = "✓ PASS" if r['overall_pass'] else "❌ FAIL"
            print(f"{r['material']:<12} {r['wavelength']:<8.0f} {r['angle']:<8.0f} "
                  f"{r['R_deviation']:<8.3%} {r['T_deviation']:<8.3%} {status:<8}")

        # Final verdict
        critical_passed = sum(1 for r in results
                            if r['material'] in ['Al2O3', 'TiN_4nm'] and r['angle'] == 0 and r['overall_pass'])

        if critical_passed >= 2:
            print(f"\n🎉 VALIDATION PROTOCOL COMPLETED SUCCESSFULLY")
            print(f"✅ MEEP and TMM yield identical R/T predictions within tolerance")
            print(f"✅ System ready for GAN optimization")
        else:
            print(f"\n💥 VALIDATION PROTOCOL FAILED")
            print(f"❌ MEEP-TMM mismatch exceeds tolerance thresholds")
            print(f"🔧 CONTINGENCY MEASURES REQUIRED:")
            print(f"   1. Increase MEEP resolution to 20+ pts/nm")
            print(f"   2. Extend simulation time to 200+ steps")
            print(f"   3. Audit material data (n/k interpolation, units)")
            print(f"   4. Fallback to analytical Fresnel equations")

def main():
    """Main validation execution"""
    if not MEEP_AVAILABLE:
        print("❌ CRITICAL: MEEP not available - validation cannot proceed")
        return

    try:
        validator = ValidationProtocol()
        results = validator.run_full_validation()

        # Save results
        if results:
            import json
            with open('validation_results.json', 'w') as f:
                json.dump(results, f, indent=2, default=str)
            print(f"\n💾 Results saved to validation_results.json")

    except Exception as e:
        print(f"❌ VALIDATION PROTOCOL FAILED: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
