#!/usr/bin/env python3
"""
MEEP-TMM VALIDATION PROTOCOL - FINAL
====================================
Mandatory validation using fully operational MEEP v1.29.0
Exact structure: 300nm PML + 500nm air + 45nm material + 500nm air + 300nm PML
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import meep as mp

print(f"✅ MEEP v{mp.__version__} - FULLY OPERATIONAL")

class MaterialDatabase:
    """Load material data with exact specifications"""
    
    def __init__(self):
        self.materials = {}
        self.load_materials()
    
    def load_materials(self):
        """Load materials with mandatory TiN-4nm data"""
        
        # Load Al2O3 reference data
        try:
            al2o3_file = Path("data/Al2O3.txt")
            if al2o3_file.exists():
                data = pd.read_csv(al2o3_file, sep='\s+', header=None, names=['wavelength', 'n', 'k'])
                self.materials['Al2O3'] = data
                print(f"✓ Al2O3 loaded: {len(data)} points ({data['wavelength'].min():.0f}-{data['wavelength'].max():.0f}nm)")
            else:
                raise FileNotFoundError("Al2O3.txt required for validation")
        except Exception as e:
            print(f"❌ CRITICAL: Al2O3 loading failed - {e}")
            exit(1)
        
        # Load TiN-4nm data directly from Excel
        try:
            tin_file = Path("data/TiN-4nm.xlsx")
            if tin_file.exists():
                data = pd.read_excel(tin_file)
                # Convert wavelength from meters to nanometers
                data['wavelength'] = data['Wavelength'] * 1e9  # Convert m to nm
                # Keep only needed columns
                data = data[['wavelength', 'n', 'k']].copy()
                # Remove any NaN values
                data = data.dropna()
                
                self.materials['TiN_4nm'] = data
                print(f"✓ TiN-4nm loaded: {len(data)} points ({data['wavelength'].min():.0f}-{data['wavelength'].max():.0f}nm)")
                print(f"  n range: {data['n'].min():.3f} - {data['n'].max():.3f}")
                print(f"  k range: {data['k'].min():.3f} - {data['k'].max():.3f}")
            else:
                raise FileNotFoundError("TiN-4nm.xlsx is MANDATORY for validation")
        except Exception as e:
            print(f"❌ CRITICAL: TiN-4nm loading failed - {e}")
            exit(1)
    
    def get_refractive_index(self, material, wavelength_nm):
        """Get complex refractive index with validation"""
        if material not in self.materials:
            raise ValueError(f"Material {material} not found in database")
        
        data = self.materials[material]
        
        # Interpolate n and k
        n = np.interp(wavelength_nm, data['wavelength'], data['n'])
        k = np.interp(wavelength_nm, data['wavelength'], data['k'])
        
        # Validation checks
        if np.isnan(n) or np.isnan(k):
            raise ValueError(f"Invalid n/k values for {material} at {wavelength_nm}nm")
        
        return n + 1j * k

class MEEPSimulator:
    """MEEP FDTD simulator with exact structure specifications"""
    
    def __init__(self, material_db):
        self.material_db = material_db
        
        # Exact structure dimensions (in micrometers for MEEP)
        self.pml_thickness = 0.3      # 300nm PML
        self.air_gap = 0.5           # 500nm air gaps
        self.material_thickness = 0.045  # 45nm material layer
        self.lateral_size = 1.0      # 1000nm lateral dimensions
        
        # Total simulation size
        self.total_size = 2 * self.pml_thickness + 2 * self.air_gap + self.material_thickness
        
        print(f"✓ MEEP structure configured:")
        print(f"  PML layers: {self.pml_thickness*1000:.0f}nm each")
        print(f"  Air gaps: {self.air_gap*1000:.0f}nm each")
        print(f"  Material layer: {self.material_thickness*1000:.0f}nm")
        print(f"  Total size: {self.total_size*1000:.0f}nm")
    
    def create_meep_material(self, material_name, wavelength_nm):
        """Create MEEP material with proper API"""
        try:
            n_complex = self.material_db.get_refractive_index(material_name, wavelength_nm)
            epsilon = n_complex ** 2
            
            # Create MEEP medium using correct API
            if n_complex.imag == 0:
                # Lossless material
                return mp.Medium(epsilon=epsilon.real)
            else:
                # Lossy material - use D_conductivity
                # Convert to MEEP units: σ = 2πnk/λ (in units of 1/μm)
                conductivity = 2 * np.pi * n_complex.imag / (wavelength_nm / 1000)  # Convert nm to μm
                return mp.Medium(epsilon=epsilon.real, D_conductivity=conductivity)
                
        except Exception as e:
            print(f"❌ MEEP material creation failed for {material_name}: {e}")
            raise
    
    def simulate_structure(self, material_name, wavelength_nm, angle_deg=0):
        """
        MEEP simulation with exact validation structure
        
        Structure (top to bottom):
        - 300nm PML
        - 500nm air (reflection detector + source)
        - 45nm material layer
        - 500nm air (transmission detector)
        - 300nm PML
        """
        try:
            print(f"\n🔬 MEEP Simulation: {material_name} @ {wavelength_nm}nm, {angle_deg}°")
            
            # Convert to MEEP units
            wavelength_um = wavelength_nm / 1000.0
            frequency = 1.0 / wavelength_um
            
            # Create material
            material = self.create_meep_material(material_name, wavelength_nm)
            
            # Define geometry
            geometry = []
            
            # Material layer at center
            material_layer = mp.Block(
                center=mp.Vector3(0, 0, 0),
                size=mp.Vector3(self.lateral_size, self.lateral_size, self.material_thickness),
                material=material
            )
            geometry.append(material_layer)
            
            # Simulation cell
            cell = mp.Vector3(self.lateral_size, self.lateral_size, self.total_size)
            
            # PML boundaries
            pml_layers = [mp.PML(thickness=self.pml_thickness)]
            
            # Source position (in first air gap)
            source_z = -self.material_thickness/2 - self.air_gap/2
            
            # Handle angled incidence
            if angle_deg == 0:
                # Normal incidence
                sources = [mp.Source(
                    mp.ContinuousSource(frequency=frequency),
                    component=mp.Ex,
                    center=mp.Vector3(0, 0, source_z)
                )]
            else:
                # Angled incidence - use plane wave
                theta = np.radians(angle_deg)
                k_vector = mp.Vector3(np.sin(theta), 0, np.cos(theta))
                sources = [mp.Source(
                    mp.ContinuousSource(frequency=frequency),
                    component=mp.Ex,
                    center=mp.Vector3(0, 0, source_z),
                    k_point=k_vector
                )]
            
            # Create simulation
            sim = mp.Simulation(
                cell_size=cell,
                boundary_layers=pml_layers,
                geometry=geometry,
                sources=sources,
                resolution=20,  # 20 points per micrometer (50nm resolution)
                force_complex_fields=True
            )
            
            # Flux monitors
            # Reflection monitor (in first air gap)
            refl_z = source_z + self.air_gap/4
            refl_region = mp.FluxRegion(
                center=mp.Vector3(0, 0, refl_z),
                size=mp.Vector3(self.lateral_size*0.8, self.lateral_size*0.8, 0)
            )
            refl_flux = sim.add_flux(frequency, 0, 1, refl_region)
            
            # Transmission monitor (in second air gap)
            trans_z = self.material_thickness/2 + self.air_gap/2
            trans_region = mp.FluxRegion(
                center=mp.Vector3(0, 0, trans_z),
                size=mp.Vector3(self.lateral_size*0.8, self.lateral_size*0.8, 0)
            )
            trans_flux = sim.add_flux(frequency, 0, 1, trans_region)
            
            # Run simulation
            print("  Running MEEP simulation...")
            sim.run(until_after_sources=mp.stop_when_fields_decayed(
                50, mp.Ex, mp.Vector3(0, 0, trans_z), 1e-9
            ))
            
            # Get flux values
            refl_data = mp.get_fluxes(refl_flux)
            trans_data = mp.get_fluxes(trans_flux)
            
            if len(refl_data) == 0 or len(trans_data) == 0:
                raise ValueError("No flux data obtained from MEEP")
            
            # Calculate R and T (need to normalize properly)
            # For now, use simple approach - will refine based on results
            incident_power = 1.0  # Normalized
            R = abs(refl_data[0]) / incident_power if incident_power > 0 else 0
            T = abs(trans_data[0]) / incident_power if incident_power > 0 else 0
            
            # Ensure physical bounds
            R = max(0, min(1, abs(R)))
            T = max(0, min(1, abs(T)))
            
            print(f"  ✓ MEEP Results: R = {R:.6f}, T = {T:.6f}")
            
            return R, T
            
        except Exception as e:
            print(f"❌ MEEP simulation failed: {e}")
            raise

class TMMSimulator:
    """Transfer Matrix Method for comparison"""

    def __init__(self, material_db):
        self.material_db = material_db

    def simulate_structure(self, material_name, wavelength_nm, angle_deg=0):
        """TMM simulation for single layer"""
        try:
            print(f"🧮 TMM Simulation: {material_name} @ {wavelength_nm}nm, {angle_deg}°")

            # Get material properties
            n_complex = self.material_db.get_refractive_index(material_name, wavelength_nm)

            # Single layer TMM calculation
            n_air = 1.0 + 0j
            n_material = n_complex

            # Layer thickness in nm
            thickness_nm = 45.0

            # Angle conversion
            theta_0 = np.radians(angle_deg)

            # Snell's law
            sin_theta_material = (n_air.real * np.sin(theta_0)) / n_material.real
            if abs(sin_theta_material) > 1:
                # Total internal reflection
                R, T = 1.0, 0.0
            else:
                theta_material = np.arcsin(sin_theta_material)

                # Wave vector
                k0 = 2 * np.pi / wavelength_nm
                beta = k0 * n_material * thickness_nm * np.cos(theta_material)

                # Transfer matrix elements
                cos_beta = np.cos(beta)
                sin_beta = np.sin(beta)

                # Admittances
                Y_air = n_air * np.cos(theta_0)
                Y_material = n_material * np.cos(theta_material)

                # Matrix elements
                m11 = cos_beta
                m12 = 1j * sin_beta / Y_material
                m21 = 1j * Y_material * sin_beta
                m22 = cos_beta

                # Reflection and transmission coefficients
                r = (Y_air * m11 + Y_air**2 * m12 - m21 - Y_air * m22) / \
                    (Y_air * m11 + Y_air**2 * m12 + m21 + Y_air * m22)

                t = 2 * Y_air / (Y_air * m11 + Y_air**2 * m12 + m21 + Y_air * m22)

                # Power coefficients
                R = abs(r)**2
                T = (Y_air.real / Y_air.real) * abs(t)**2

            print(f"  ✓ TMM Results: R = {R:.6f}, T = {T:.6f}")

            return R, T

        except Exception as e:
            print(f"❌ TMM simulation failed: {e}")
            raise

def run_meep_tmm_validation():
    """Execute MANDATORY MEEP-TMM validation protocol"""
    print("\n" + "="*80)
    print("MANDATORY MEEP-TMM VALIDATION PROTOCOL")
    print("="*80)
    print("Using fully operational MEEP v1.29.0 for FDTD simulations")
    print("Structure: 300nm PML + 500nm air + 45nm material + 500nm air + 300nm PML")

    # Initialize
    material_db = MaterialDatabase()
    meep_sim = MEEPSimulator(material_db)
    tmm_sim = TMMSimulator(material_db)

    # Validation thresholds
    thresholds = {
        'Al2O3': {'R': 0.01, 'T': 0.01},    # ≤1% deviation
        'TiN_4nm': {'R': 0.02, 'T': 0.02}   # ≤2% deviation
    }

    results = []

    # Phase 1: Al2O3 Benchmark
    print(f"\n🔬 PHASE 1: Al2O3 BENCHMARK")
    print(f"{'='*50}")

    try:
        R_meep, T_meep = meep_sim.simulate_structure('Al2O3', 800, 0)
        R_tmm, T_tmm = tmm_sim.simulate_structure('Al2O3', 800, 0)

        # Calculate deviations
        R_deviation = abs(R_meep - R_tmm) / R_tmm if R_tmm > 0 else abs(R_meep - R_tmm)
        T_deviation = abs(T_meep - T_tmm) / T_tmm if T_tmm > 0 else abs(T_meep - T_tmm)

        # Validation
        thresh = thresholds['Al2O3']
        R_pass = R_deviation <= thresh['R']
        T_pass = T_deviation <= thresh['T']
        overall_pass = R_pass and T_pass

        print(f"\n📊 Al2O3 COMPARISON:")
        print(f"{'Method':<8} {'R':<10} {'T':<10}")
        print(f"{'-'*30}")
        print(f"{'MEEP':<8} {R_meep:<10.6f} {T_meep:<10.6f}")
        print(f"{'TMM':<8} {R_tmm:<10.6f} {T_tmm:<10.6f}")
        print(f"{'Deviation':<8} {R_deviation:<10.4%} {T_deviation:<10.4%}")
        print(f"{'Threshold':<8} {thresh['R']:<10.4%} {thresh['T']:<10.4%}")

        print(f"\n🔍 VALIDATION:")
        print(f"  R: {'✓ PASS' if R_pass else '❌ FAIL'} ({R_deviation:.4%})")
        print(f"  T: {'✓ PASS' if T_pass else '❌ FAIL'} ({T_deviation:.4%})")
        print(f"  Overall: {'🎉 PASS' if overall_pass else '💥 FAIL'}")

        results.append({
            'material': 'Al2O3',
            'R_meep': R_meep,
            'T_meep': T_meep,
            'R_tmm': R_tmm,
            'T_tmm': T_tmm,
            'R_deviation': R_deviation,
            'T_deviation': T_deviation,
            'overall_pass': overall_pass
        })

        if not overall_pass:
            print("❌ Al2O3 benchmark FAILED - Cannot proceed to TiN")
            return results

    except Exception as e:
        print(f"❌ Al2O3 validation failed: {e}")
        return results

    # Phase 2: TiN-4nm Validation
    print(f"\n🔬 PHASE 2: TiN-4nm VALIDATION")
    print(f"{'='*50}")

    try:
        R_meep, T_meep = meep_sim.simulate_structure('TiN_4nm', 800, 0)
        R_tmm, T_tmm = tmm_sim.simulate_structure('TiN_4nm', 800, 0)

        # Calculate deviations
        R_deviation = abs(R_meep - R_tmm) / R_tmm if R_tmm > 0 else abs(R_meep - R_tmm)
        T_deviation = abs(T_meep - T_tmm) / T_tmm if T_tmm > 0 else abs(T_meep - T_tmm)

        # Validation
        thresh = thresholds['TiN_4nm']
        R_pass = R_deviation <= thresh['R']
        T_pass = T_deviation <= thresh['T']
        overall_pass = R_pass and T_pass

        print(f"\n📊 TiN-4nm COMPARISON:")
        print(f"{'Method':<8} {'R':<10} {'T':<10}")
        print(f"{'-'*30}")
        print(f"{'MEEP':<8} {R_meep:<10.6f} {T_meep:<10.6f}")
        print(f"{'TMM':<8} {R_tmm:<10.6f} {T_tmm:<10.6f}")
        print(f"{'Deviation':<8} {R_deviation:<10.4%} {T_deviation:<10.4%}")
        print(f"{'Threshold':<8} {thresh['R']:<10.4%} {thresh['T']:<10.4%}")

        print(f"\n🔍 VALIDATION:")
        print(f"  R: {'✓ PASS' if R_pass else '❌ FAIL'} ({R_deviation:.4%})")
        print(f"  T: {'✓ PASS' if T_pass else '❌ FAIL'} ({T_deviation:.4%})")
        print(f"  Overall: {'🎉 PASS' if overall_pass else '💥 FAIL'}")

        results.append({
            'material': 'TiN_4nm',
            'R_meep': R_meep,
            'T_meep': T_meep,
            'R_tmm': R_tmm,
            'T_tmm': T_tmm,
            'R_deviation': R_deviation,
            'T_deviation': T_deviation,
            'overall_pass': overall_pass
        })

    except Exception as e:
        print(f"❌ TiN-4nm validation failed: {e}")

    # Final Summary
    print(f"\n" + "="*80)
    print("MEEP-TMM VALIDATION SUMMARY")
    print("="*80)

    passed = sum(1 for r in results if r['overall_pass'])
    total = len(results)

    print(f"Success Rate: {passed}/{total} ({passed/total*100:.1f}%)")

    if passed >= 2:
        print(f"\n🎉 VALIDATION PROTOCOL COMPLETED SUCCESSFULLY")
        print(f"✅ MEEP and TMM yield identical R/T predictions within tolerance")
        print(f"✅ System ready for GAN optimization")
    else:
        print(f"\n💥 VALIDATION PROTOCOL FAILED")
        print(f"❌ MEEP-TMM mismatch exceeds tolerance thresholds")

    return results

if __name__ == "__main__":
    results = run_meep_tmm_validation()

    # Save results
    import json
    with open('meep_tmm_validation_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    print(f"\n💾 Results saved to meep_tmm_validation_results.json")
