# GAN for Electromagnetic Simulation with PyMEEP

## 🎯 Overview

This implementation provides a complete **Generative Adversarial Network (GAN) architecture** for optimizing 45nm electromagnetic material structures using **real PyMEEP simulations**. The system follows your exact specifications for electromagnetic optimization with experimental data validation.

## 🏗️ Architecture

### Structure Layout (Top to Bottom)
```
┌─────────────────────────────────────┐
│        300nm PML Layer              │  ← Top PML (Perfectly Matched Layer)
├─────────────────────────────────────┤
│    500nm+ Reflection Detector       │  ← Light source region
│         & Light Source              │
├─────────────────────────────────────┤
│      45nm Material Structure        │  ← **OPTIMIZED BY GAN**
│   (4 materials: TiN_4nm, TiO2,     │
│    Al2O3, TiN_30nm)                 │
├─────────────────────────────────────┤
│   500nm+ Transmission Detector      │
├─────────────────────────────────────┤
│        300nm PML Layer              │  ← Bottom PML (Perfectly Matched Layer)
└─────────────────────────────────────┘
```

**IMPORTANT**: The structure requires **TWO PML layers** - one at the top and one at the bottom to properly absorb electromagnetic waves and prevent reflections from simulation boundaries.

### GAN Components

#### 1. **Generator (G)**
- **Input**: Random noise vector z (100D)
- **Output**: 3D material distribution (100×50×100×4)
- **Architecture**: Deep 3D convolutional neural network
- **Materials**: 4 materials as specified:
  1. TiN_4nm (thin film)
  2. TiO2
  3. Al2O3
  4. TiN_30nm

#### 2. **Discriminator (D)**
- **Input**: 3D material distribution (100×50×100×4)
- **Output**: Probability that input is real [0,1]
- **Purpose**: Distinguishes real vs fake structural patterns

#### 3. **Surrogate Simulator (S)**
- **Input**: 3D material distribution + wavelength + angle + polarization
- **Output**: Reflectance (R) and Transmittance (T)
- **Purpose**: Ultrafast proxy for full FDTD simulation

#### 4. **MEEP Simulator**
- **Real PyMEEP integration** for electromagnetic simulation
- **Validation**: Compares surrogate predictions with full FDTD
- **Structure**: Converts 3D material maps to MEEP geometry

## 📊 Loss Function

The physics-based loss function matches your specification:

```
Loss = α₁ × ||(Rsim - Rexp) / Rexp|| + α₂ × ||(Tsim - Texp) / Texp||
```

Where:
- **α₁ = α₂ = 0.5** (initial weights)
- **Rsim, Tsim**: Simulated reflectance/transmittance
- **Rexp, Texp**: Experimental data from CSV files
- **Wavelength range**: 300-2600nm
- **Incident angles**: 15°, 30°, 45°, 60°

## 📁 File Structure

```
gan_meep_electromagnetic.py    # Main GAN implementation
test_gan_meep.py              # Comprehensive test suite
quick_test_gan.py             # Quick 5-epoch test
README_GAN_MEEP.md           # This documentation

data/                        # Experimental data
├── TiN-4nm.xlsx            # Material n,k data
├── Al2O3.txt               # Material n,k data
├── RT_15degree_SP.csv      # Experimental R,T at 15°
├── RT_30degree_SP.csv      # Experimental R,T at 30°
├── RT_45degree_SP.csv      # Experimental R,T at 45°
└── RT_60degree_SP.csv      # Experimental R,T at 60°

results/                     # Training outputs
├── best_structure.npy      # Optimized material structure
├── training_history.csv    # Loss curves
├── training_curves.png     # Visualization
└── structure_epoch_*.png   # Generated structures
```

## 🚀 Usage

### Quick Test (5 epochs)
```bash
python quick_test_gan.py
```

### Full Training (100 epochs)
```bash
python gan_meep_electromagnetic.py
```

### Run Tests
```bash
python test_gan_meep.py
```

## 📈 Training Process

1. **Validation Phase**: 
   - Compare MEEP vs TMM for simple structures
   - Verify material database loading

2. **GAN Training Phase**:
   - **Discriminator training**: Real vs fake patterns
   - **Generator training**: Adversarial + physics loss
   - **Surrogate training**: Fast R/T prediction
   - **MEEP validation**: Full electromagnetic simulation

3. **Optimization Target**:
   - Minimize loss across all wavelengths and angles
   - Output best structure when loss is sufficiently small

## 🔧 Installation

### Required Dependencies
```bash
# Core dependencies
pip install torch torchvision pandas scipy matplotlib openpyxl

# For real electromagnetic simulation
conda install -c conda-forge pymeeus  # PyMEEP
```

### System Requirements
- **GPU recommended** (CUDA support)
- **PyMEEP** for real electromagnetic simulation
- **Python 3.8+**

## 📊 Results Analysis

The system provides comprehensive analysis:

### Material Composition
- Percentage of each material in optimized structure
- Layer-by-layer analysis (z-direction)

### Performance Metrics
- **Physics loss convergence**
- **R/T matching accuracy** across wavelengths/angles
- **MEEP validation** at multiple wavelengths

### Visualization
- **3D structure slices** (XY, XZ, YZ)
- **Material distribution histograms**
- **Training loss curves**

## ⚡ Key Features

✅ **Real PyMEEP Integration** - Actual electromagnetic simulation  
✅ **4-Material Optimization** - TiN_4nm, TiO2, Al2O3, TiN_30nm  
✅ **Multi-angle Validation** - 15°, 30°, 45°, 60° incident angles  
✅ **Broadband Optimization** - 300-2600nm wavelength range  
✅ **Physics-based Loss** - Matches experimental R/T data  
✅ **GPU Acceleration** - CUDA support for fast training  
✅ **Comprehensive Analysis** - Detailed structure characterization  

## 🎯 Next Steps

1. **Install PyMEEP**: `conda install -c conda-forge pymeeus`
2. **Add your experimental data** to `data/` directory
3. **Run full training**: `python gan_meep_electromagnetic.py`
4. **Analyze results** in `results/` directory

The implementation is ready for your electromagnetic optimization tasks!
