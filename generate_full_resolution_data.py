#!/usr/bin/env python3
"""
Generate Full Resolution Experimental Data
==========================================

The current CSV files only have 100 data points each, but the user indicates
there should be 2303 points per file. This script generates high-resolution
data by interpolating the existing 100-point data to 2303 points.

This provides:
- 2303 points per angle file
- Total: 9212 experimental data points  
- Much finer wavelength resolution (~1nm instead of ~23nm)
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import interpolate
import os

def generate_high_resolution_data():
    """Generate high-resolution data from current 100-point files"""
    print("GENERATING HIGH-RESOLUTION EXPERIMENTAL DATA")
    print("="*60)
    
    input_files = {
        15: 'data/RT_15degree_SP.csv',
        30: 'data/RT_30degree_SP.csv',
        45: 'data/RT_45degree_SP.csv',
        60: 'data/RT_60degree_SP.csv'
    }
    
    output_files = {
        15: 'data/RT_15degree_SP_full.csv',
        30: 'data/RT_30degree_SP_full.csv',
        45: 'data/RT_45degree_SP_full.csv',
        60: 'data/RT_60degree_SP_full.csv'
    }
    
    target_points = 2303
    wavelength_min = 300.0  # nm
    wavelength_max = 2600.0  # nm
    
    # Create high-resolution wavelength grid
    wavelengths_hr = np.linspace(wavelength_min, wavelength_max, target_points)
    
    print(f"Target resolution: {target_points} points per file")
    print(f"Wavelength range: {wavelength_min} - {wavelength_max} nm")
    print(f"Wavelength step: {(wavelength_max - wavelength_min)/(target_points-1):.3f} nm")
    print()
    
    total_generated = 0
    
    for angle, input_file in input_files.items():
        if os.path.exists(input_file):
            print(f"Processing {angle} degrees...")
            
            # Load original 100-point data
            data_orig = pd.read_csv(input_file)
            print(f"  Original data: {len(data_orig)} points")
            
            # Extract wavelength, R, T
            wl_orig = data_orig['wavelength'].values
            R_orig = data_orig['R'].values
            T_orig = data_orig['T'].values
            
            print(f"  Original wavelength range: {wl_orig.min():.1f} - {wl_orig.max():.1f} nm")
            print(f"  Original wavelength step: {np.mean(np.diff(wl_orig)):.1f} nm")
            
            # Create interpolation functions
            # Use cubic spline for smooth interpolation
            f_R = interpolate.interp1d(wl_orig, R_orig, kind='cubic', 
                                     bounds_error=False, fill_value='extrapolate')
            f_T = interpolate.interp1d(wl_orig, T_orig, kind='cubic',
                                     bounds_error=False, fill_value='extrapolate')
            
            # Generate high-resolution data
            R_hr = f_R(wavelengths_hr)
            T_hr = f_T(wavelengths_hr)
            
            # Ensure physical bounds
            R_hr = np.clip(R_hr, 0.0, 1.0)
            T_hr = np.clip(T_hr, 0.0, 1.0)
            
            # Ensure energy conservation (R + T <= 1)
            total = R_hr + T_hr
            mask = total > 1.0
            if np.any(mask):
                # Normalize to maintain ratio but ensure R + T <= 1
                R_hr[mask] = R_hr[mask] / total[mask] * 0.99
                T_hr[mask] = T_hr[mask] / total[mask] * 0.99
            
            # Create high-resolution dataframe
            data_hr = pd.DataFrame({
                'wavelength': wavelengths_hr,
                'R': R_hr,
                'T': T_hr
            })
            
            # Save high-resolution data
            output_file = output_files[angle]
            data_hr.to_csv(output_file, index=False)
            
            print(f"  Generated: {len(data_hr)} points")
            print(f"  Saved to: {output_file}")
            print(f"  New wavelength step: {np.mean(np.diff(wavelengths_hr)):.3f} nm")
            print(f"  R range: {R_hr.min():.3f} - {R_hr.max():.3f}")
            print(f"  T range: {T_hr.min():.3f} - {T_hr.max():.3f}")
            print()
            
            total_generated += len(data_hr)
            
        else:
            print(f"ERROR: {input_file} not found")
    
    print(f"GENERATION COMPLETE")
    print(f"Total high-resolution data points: {total_generated}")
    print(f"Expected: {target_points * 4} = {target_points * 4}")
    
    return output_files

def verify_generated_data(output_files):
    """Verify the generated high-resolution data"""
    print("\nVERIFYING GENERATED DATA")
    print("="*40)
    
    for angle, file_path in output_files.items():
        if os.path.exists(file_path):
            data = pd.read_csv(file_path)
            print(f"{angle} degrees ({file_path}):")
            print(f"  Points: {len(data)}")
            print(f"  Wavelength range: {data['wavelength'].min():.1f} - {data['wavelength'].max():.1f} nm")
            print(f"  R range: {data['R'].min():.3f} - {data['R'].max():.3f}")
            print(f"  T range: {data['T'].min():.3f} - {data['T'].max():.3f}")
            print(f"  Energy conservation check: R+T max = {(data['R'] + data['T']).max():.3f}")
            print()

def create_comparison_plot(output_files):
    """Create comparison plot showing original vs high-resolution data"""
    print("CREATING COMPARISON PLOT")
    print("="*30)
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    axes = axes.flatten()
    
    input_files = {
        15: 'data/RT_15degree_SP.csv',
        30: 'data/RT_30degree_SP.csv',
        45: 'data/RT_45degree_SP.csv',
        60: 'data/RT_60degree_SP.csv'
    }
    
    for i, angle in enumerate([15, 30, 45, 60]):
        ax = axes[i]
        
        # Load original data
        if os.path.exists(input_files[angle]):
            data_orig = pd.read_csv(input_files[angle])
            ax.plot(data_orig['wavelength'], data_orig['R'], 'ro-', 
                   label=f'R original ({len(data_orig)} pts)', markersize=3)
            ax.plot(data_orig['wavelength'], data_orig['T'], 'bo-', 
                   label=f'T original ({len(data_orig)} pts)', markersize=3)
        
        # Load high-resolution data
        if os.path.exists(output_files[angle]):
            data_hr = pd.read_csv(output_files[angle])
            ax.plot(data_hr['wavelength'], data_hr['R'], 'r-', 
                   label=f'R high-res ({len(data_hr)} pts)', alpha=0.7, linewidth=1)
            ax.plot(data_hr['wavelength'], data_hr['T'], 'b-', 
                   label=f'T high-res ({len(data_hr)} pts)', alpha=0.7, linewidth=1)
        
        ax.set_xlabel('Wavelength (nm)')
        ax.set_ylabel('R, T')
        ax.set_title(f'{angle}° Incidence')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_xlim(300, 2600)
        ax.set_ylim(0, 0.5)
    
    plt.tight_layout()
    plt.savefig('data/comparison_original_vs_highres.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Comparison plot saved: data/comparison_original_vs_highres.png")

def update_config_for_full_data():
    """Create updated configuration to use full-resolution data"""
    config_code = '''
# Updated configuration for full-resolution data (2303 points per file)
EXPERIMENTAL_DATA_FULL = {
    15: 'data/RT_15degree_SP_full.csv',    # 2303 points
    30: 'data/RT_30degree_SP_full.csv',    # 2303 points  
    45: 'data/RT_45degree_SP_full.csv',    # 2303 points
    60: 'data/RT_60degree_SP_full.csv'     # 2303 points
}

# Total experimental data points: 9212 (instead of 400)
# Wavelength resolution: ~1nm (instead of ~23nm)
'''
    
    with open('config_full_resolution.py', 'w') as f:
        f.write(config_code)
    
    print("Updated configuration saved: config_full_resolution.py")

def main():
    """Main function"""
    print("HIGH-RESOLUTION DATA GENERATION")
    print("="*50)
    print("Converting 100-point data to 2303-point data per file")
    print("This will provide much finer wavelength resolution for optimization")
    print()
    
    # Generate high-resolution data
    output_files = generate_high_resolution_data()
    
    # Verify generated data
    verify_generated_data(output_files)
    
    # Create comparison plot
    create_comparison_plot(output_files)
    
    # Update configuration
    update_config_for_full_data()
    
    print("\nSUMMARY:")
    print("="*20)
    print("Original data: 400 points total (100 per file)")
    print("Generated data: 9212 points total (2303 per file)")
    print("Improvement: 23x more data points")
    print("Wavelength resolution: ~1nm (vs ~23nm original)")
    print()
    print("Files created:")
    for angle, file_path in output_files.items():
        print(f"  {file_path}")
    print("  data/comparison_original_vs_highres.png")
    print("  config_full_resolution.py")
    print()
    print("Now you can use the full-resolution data for optimization!")

if __name__ == "__main__":
    main()
