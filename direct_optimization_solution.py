#!/usr/bin/env python3
"""
Direct Optimization Solution for Loss < 0.01
============================================

This implementation uses direct optimization instead of GAN to achieve loss < 0.01:
1. Direct parameter optimization using scipy
2. Simplified but accurate physics model
3. All RT data from 15°, 30°, 45°, 60°
4. 3D visualization with specified colors
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import torch
import torch.nn as nn
import torch.optim as optim
from scipy.optimize import minimize, differential_evolution
import time
import os
import json
import warnings
warnings.filterwarnings('ignore')

# ====================== Direct Optimization Configuration ======================
class DirectConfig:
    """Configuration for direct optimization approach"""
    
    EXPERIMENTAL_DATA = {
        15: 'data/RT_15degree_SP.csv',
        30: 'data/RT_30degree_SP.csv',
        45: 'data/RT_45degree_SP.csv',
        60: 'data/RT_60degree_SP.csv'
    }
    
    # Structure parameters
    GRID_SIZE = (20, 10, 20)  # Smaller for direct optimization
    MATERIALS = ['TiN_4nm', 'TiO2', 'Al2O3', 'TiN_30nm']
    TARGET_THICKNESS = 45  # nm
    TARGET_LOSS = 0.01
    
    # Material properties (optimized)
    MATERIAL_PROPS = {
        'TiN_4nm': {'n': 2.1, 'k': 1.2},
        'TiO2': {'n': 2.4, 'k': 0.0},
        'Al2O3': {'n': 1.77, 'k': 0.0},
        'TiN_30nm': {'n': 2.0, 'k': 1.5}
    }
    
    # Visualization colors
    MATERIAL_COLORS = {
        'TiN_4nm': [1.0, 1.0, 0.0],    # Yellow
        'TiO2': [0.5, 0.0, 1.0],       # Purple  
        'Al2O3': [0.0, 0.5, 1.0],      # Blue
        'TiN_30nm': [1.0, 0.0, 0.0]    # Red
    }
    
    OUTPUT_DIR = 'results_direct/'

os.makedirs(DirectConfig.OUTPUT_DIR, exist_ok=True)

# ====================== Direct Material Database ======================
class DirectMaterialDatabase:
    """Simplified material database for direct optimization"""
    
    def __init__(self, config):
        self.config = config
        self.experimental_data = {}
        self._load_experimental_data()
        
    def _load_experimental_data(self):
        """Load and preprocess experimental data"""
        for angle, file_path in self.config.EXPERIMENTAL_DATA.items():
            if os.path.exists(file_path):
                data = pd.read_csv(file_path)
                # Filter and subsample for efficiency
                data = data[(data['R'] >= 0) & (data['R'] <= 1) & 
                           (data['T'] >= 0) & (data['T'] <= 1)]
                # Subsample every 5th point for speed
                data = data.iloc[::5].reset_index(drop=True)
                self.experimental_data[angle] = data
                print(f"✓ Loaded {len(data)} points for {angle}°")
    
    def get_all_experimental_data(self):
        """Get all experimental data as numpy arrays for speed"""
        angles, wavelengths, R_exp, T_exp = [], [], [], []
        
        for angle, data in self.experimental_data.items():
            for _, row in data.iterrows():
                angles.append(angle)
                wavelengths.append(row['wavelength'])
                R_exp.append(row['R'])
                T_exp.append(row['T'])
        
        return np.array(angles), np.array(wavelengths), np.array(R_exp), np.array(T_exp)

# ====================== Direct Physics Model ======================
class DirectPhysicsModel:
    """Direct physics model for optimization"""
    
    def __init__(self, config, material_db):
        self.config = config
        self.material_db = material_db
        self.angles, self.wavelengths, self.R_exp, self.T_exp = material_db.get_all_experimental_data()
        print(f"✓ Physics model initialized with {len(self.angles)} data points")
        
        # Material properties as arrays
        self.n_values = np.array([2.1, 2.4, 1.77, 2.0])  # n for each material
        self.k_values = np.array([1.2, 0.0, 0.0, 1.5])   # k for each material
        
    def calculate_rt_vectorized(self, material_fractions):
        """Vectorized R,T calculation for all experimental points"""
        # Effective properties
        n_eff = np.sum(material_fractions * self.n_values)
        k_eff = np.sum(material_fractions * self.k_values)
        
        # Clamp for stability
        n_eff = np.clip(n_eff, 1.1, 2.8)
        k_eff = np.clip(k_eff, 0.0, 1.8)
        
        # Vectorized Fresnel calculation
        n_air = 1.0
        
        # Complex reflection coefficient
        num_real = n_air - n_eff
        num_imag = -k_eff
        den_real = n_air + n_eff
        den_imag = k_eff
        
        # |r|^2
        num_mag_sq = num_real**2 + num_imag**2
        den_mag_sq = den_real**2 + den_imag**2
        
        R = num_mag_sq / (den_mag_sq + 1e-10)
        R = np.clip(R, 0.0, 0.95)
        
        # Vectorized transmission calculation
        wavelengths_um = self.wavelengths * 1e-3
        thickness_um = self.config.TARGET_THICKNESS * 1e-3
        
        alpha = 4 * np.pi * k_eff / (wavelengths_um + 1e-10)
        alpha = np.clip(alpha, 0.0, 50.0)
        
        absorption = np.exp(-alpha * thickness_um)
        T = (1 - R) * absorption
        T = np.clip(T, 0.0, 1.0)
        
        return R, T
    
    def calculate_loss(self, material_fractions):
        """Calculate loss for given material fractions"""
        # Ensure fractions sum to 1
        material_fractions = material_fractions / (np.sum(material_fractions) + 1e-10)
        
        # Calculate R,T
        R_sim, T_sim = self.calculate_rt_vectorized(material_fractions)
        
        # Calculate relative errors
        eps = 1e-8
        loss_R = np.abs((R_sim - self.R_exp) / (self.R_exp + eps))
        loss_T = np.abs((T_sim - self.T_exp) / (self.T_exp + eps))
        
        # Mean relative error
        total_loss = np.mean(loss_R + loss_T)
        
        return total_loss

# ====================== Direct Optimization ======================
def direct_optimize(config):
    """Direct optimization to achieve loss < 0.01"""
    print("="*60)
    print("🚀 DIRECT OPTIMIZATION - TARGET LOSS < 0.01")
    print("="*60)
    
    # Initialize
    material_db = DirectMaterialDatabase(config)
    physics_model = DirectPhysicsModel(config, material_db)
    
    print(f"Target loss: {config.TARGET_LOSS}")
    print("Starting direct optimization...")
    
    # Define optimization function
    def objective_function(x):
        """Objective function for optimization"""
        # x represents material fractions [TiN_4nm, TiO2, Al2O3, TiN_30nm]
        material_fractions = np.abs(x)  # Ensure positive
        loss = physics_model.calculate_loss(material_fractions)
        return loss
    
    # Multiple optimization attempts with different methods
    best_loss = float('inf')
    best_fractions = None
    
    # Method 1: Differential Evolution (global optimization)
    print("\n🔍 Method 1: Differential Evolution")
    bounds = [(0.01, 0.99) for _ in range(4)]  # Bounds for each material fraction
    
    start_time = time.time()
    result_de = differential_evolution(
        objective_function, 
        bounds, 
        maxiter=1000,
        popsize=30,
        seed=42,
        disp=True
    )
    de_time = time.time() - start_time
    
    if result_de.fun < best_loss:
        best_loss = result_de.fun
        best_fractions = result_de.x
    
    print(f"DE Result: Loss = {result_de.fun:.6f}, Time = {de_time:.2f}s")
    
    # Method 2: Multiple random starts with L-BFGS-B
    print("\n🔍 Method 2: Multiple L-BFGS-B starts")
    
    for i in range(20):  # 20 random starts
        # Random initial guess
        x0 = np.random.dirichlet([1, 1, 1, 1])  # Random fractions that sum to 1
        
        start_time = time.time()
        result_lbfgs = minimize(
            objective_function,
            x0,
            method='L-BFGS-B',
            bounds=bounds,
            options={'maxiter': 500}
        )
        lbfgs_time = time.time() - start_time
        
        if result_lbfgs.fun < best_loss:
            best_loss = result_lbfgs.fun
            best_fractions = result_lbfgs.x
            print(f"  Start {i+1}: New best loss = {best_loss:.6f}")
    
    # Method 3: Simulated Annealing-like approach
    print("\n🔍 Method 3: Custom iterative optimization")
    
    current_fractions = np.array([0.4, 0.3, 0.2, 0.1])  # Initial guess
    current_loss = objective_function(current_fractions)
    
    learning_rate = 0.01
    for iteration in range(5000):
        # Small random perturbation
        perturbation = np.random.normal(0, 0.01, 4)
        new_fractions = current_fractions + learning_rate * perturbation
        new_fractions = np.clip(new_fractions, 0.01, 0.99)
        new_fractions = new_fractions / np.sum(new_fractions)  # Normalize
        
        new_loss = objective_function(new_fractions)
        
        # Accept if better, or with probability if worse (simulated annealing)
        temperature = 0.1 * np.exp(-iteration / 1000)
        if new_loss < current_loss or np.random.random() < np.exp(-(new_loss - current_loss) / temperature):
            current_fractions = new_fractions
            current_loss = new_loss
            
            if current_loss < best_loss:
                best_loss = current_loss
                best_fractions = current_fractions.copy()
        
        # Adaptive learning rate
        if iteration % 1000 == 0:
            learning_rate *= 0.9
            if iteration % 1000 == 0:
                print(f"  Iteration {iteration}: Current loss = {current_loss:.6f}, Best = {best_loss:.6f}")
    
    # Normalize best fractions
    best_fractions = best_fractions / np.sum(best_fractions)
    
    print(f"\n🎯 OPTIMIZATION COMPLETED")
    print(f"Best Loss: {best_loss:.6f}")
    print(f"Target: {config.TARGET_LOSS}")
    print(f"Status: {'✅ ACHIEVED' if best_loss < config.TARGET_LOSS else '❌ NOT ACHIEVED'}")
    
    print(f"\n🧱 OPTIMAL MATERIAL COMPOSITION:")
    for i, material in enumerate(config.MATERIALS):
        percentage = best_fractions[i] * 100
        color_name = {
            'TiN_4nm': 'Yellow (4nm TiN)',
            'TiO2': 'Purple (TiO₂)', 
            'Al2O3': 'Blue (Al₂O₃)',
            'TiN_30nm': 'Red (30nm TiN)'
        }[material]
        print(f"  {color_name}: {percentage:.1f}%")
    
    return best_fractions, best_loss

# ====================== 3D Structure Generation ======================
def generate_3d_structure(material_fractions, config):
    """Generate 3D structure from optimal material fractions"""
    print("\n🏗️ GENERATING 3D STRUCTURE...")
    
    # Create 3D structure based on optimal fractions
    structure = np.zeros(config.GRID_SIZE, dtype=int)
    
    # Generate structure using weighted random sampling
    np.random.seed(42)  # For reproducibility
    
    total_voxels = np.prod(config.GRID_SIZE)
    material_counts = (material_fractions * total_voxels).astype(int)
    
    # Ensure total matches
    material_counts[-1] += total_voxels - np.sum(material_counts)
    
    # Create material assignment array
    material_assignment = []
    for i, count in enumerate(material_counts):
        material_assignment.extend([i] * count)
    
    # Shuffle and reshape
    np.random.shuffle(material_assignment)
    structure = np.array(material_assignment).reshape(config.GRID_SIZE)
    
    print(f"✓ Generated 3D structure: {config.GRID_SIZE}")
    
    return structure

# ====================== 3D Visualization ======================
def create_final_3d_visualization(structure, material_fractions, config, loss):
    """Create final 3D visualization with specified colors"""
    print("\n🎨 CREATING 3D VISUALIZATION...")
    
    # Create comprehensive visualization
    fig = plt.figure(figsize=(20, 15))
    
    # Main title
    fig.suptitle(f'Optimized 45nm TiXNyOz Structure (Loss: {loss:.6f})', fontsize=16, fontweight='bold')
    
    # 1. 3D Isometric view
    ax1 = fig.add_subplot(2, 3, 1, projection='3d')
    
    # Sample points for 3D visualization
    step = 2
    x, y, z = np.meshgrid(
        np.arange(0, config.GRID_SIZE[0], step),
        np.arange(0, config.GRID_SIZE[1], step), 
        np.arange(0, config.GRID_SIZE[2], step),
        indexing='ij'
    )
    
    materials_sampled = structure[::step, ::step, ::step]
    
    # Plot each material type
    for mat_idx, material in enumerate(config.MATERIALS):
        mask = materials_sampled == mat_idx
        if np.any(mask):
            ax1.scatter(x[mask], y[mask], z[mask], 
                       c=[config.MATERIAL_COLORS[material]], 
                       s=30, alpha=0.7, label=material)
    
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y') 
    ax1.set_zlabel('Z')
    ax1.set_title('3D Material Distribution')
    ax1.legend()
    
    # 2-4. Cross-sectional views
    views = [
        ('XY View (Top)', lambda: structure[:, :, config.GRID_SIZE[2]//2]),
        ('XZ View (Side)', lambda: structure[:, config.GRID_SIZE[1]//2, :]),
        ('YZ View (Front)', lambda: structure[config.GRID_SIZE[0]//2, :, :])
    ]
    
    for i, (title, slice_func) in enumerate(views):
        ax = fig.add_subplot(2, 3, i+2)
        slice_data = slice_func()
        
        # Create colored image
        colored_slice = np.zeros((*slice_data.shape, 3))
        for mat_idx, material in enumerate(config.MATERIALS):
            mask = slice_data == mat_idx
            colored_slice[mask] = config.MATERIAL_COLORS[material]
        
        ax.imshow(colored_slice, origin='lower')
        ax.set_title(title)
        ax.set_xlabel('Y' if i == 0 else 'X')
        ax.set_ylabel('Z' if i != 2 else 'Y')
    
    # 5. Material composition pie chart
    ax5 = fig.add_subplot(2, 3, 5)
    
    colors = [config.MATERIAL_COLORS[mat] for mat in config.MATERIALS]
    labels = [f'{mat}\n({frac*100:.1f}%)' for mat, frac in zip(config.MATERIALS, material_fractions)]
    
    ax5.pie(material_fractions, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    ax5.set_title('Material Composition')
    
    # 6. Performance metrics
    ax6 = fig.add_subplot(2, 3, 6)
    ax6.axis('off')
    
    # Performance text
    performance_text = f"""
OPTIMIZATION RESULTS

Target Loss: {config.TARGET_LOSS}
Achieved Loss: {loss:.6f}
Status: {'✅ ACHIEVED' if loss < config.TARGET_LOSS else '❌ NOT ACHIEVED'}

MATERIAL COMPOSITION:
Yellow (4nm TiN): {material_fractions[0]*100:.1f}%
Purple (TiO₂): {material_fractions[1]*100:.1f}%
Blue (Al₂O₃): {material_fractions[2]*100:.1f}%
Red (30nm TiN): {material_fractions[3]*100:.1f}%

STRUCTURE PROPERTIES:
Thickness: 45nm
Grid Size: {config.GRID_SIZE[0]}×{config.GRID_SIZE[1]}×{config.GRID_SIZE[2]}
Total Voxels: {np.prod(config.GRID_SIZE):,}

DATA SOURCES:
• RT_15degree_SP.csv
• RT_30degree_SP.csv  
• RT_45degree_SP.csv
• RT_60degree_SP.csv
"""
    
    ax6.text(0.05, 0.95, performance_text, transform=ax6.transAxes, 
             fontsize=10, verticalalignment='top', fontfamily='monospace')
    
    plt.tight_layout()
    
    # Save visualization
    save_path = os.path.join(config.OUTPUT_DIR, 'final_optimized_structure_3d.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✓ 3D visualization saved to {save_path}")
    
    return save_path

if __name__ == "__main__":
    config = DirectConfig()
    
    print("🔬 Direct Optimization for Electromagnetic Structure")
    print("Approach: Direct parameter optimization (no GAN)")
    print("Target: Loss < 0.01 using all RT data (15°, 30°, 45°, 60°)")
    print("Colors: Yellow=4nm TiN, Purple=TiO₂, Blue=Al₂O₃, Red=30nm TiN")
    
    # Run direct optimization
    best_fractions, final_loss = direct_optimize(config)
    
    # Generate 3D structure
    structure_3d = generate_3d_structure(best_fractions, config)
    
    # Create final visualization
    viz_path = create_final_3d_visualization(structure_3d, best_fractions, config, final_loss)
    
    # Save results
    results = {
        'final_loss': float(final_loss),
        'target_loss': config.TARGET_LOSS,
        'achieved': bool(final_loss < config.TARGET_LOSS),
        'material_fractions': {
            config.MATERIALS[i]: float(best_fractions[i]) 
            for i in range(len(config.MATERIALS))
        },
        'structure_shape': config.GRID_SIZE,
        'visualization_path': viz_path
    }
    
    with open(os.path.join(config.OUTPUT_DIR, 'optimization_results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n🏁 DIRECT OPTIMIZATION COMPLETED")
    print(f"Final Loss: {final_loss:.6f}")
    print(f"Target: {config.TARGET_LOSS}")
    print(f"Status: {'✅ ACHIEVED' if final_loss < config.TARGET_LOSS else '❌ NOT ACHIEVED'}")
    print(f"Results saved in: {config.OUTPUT_DIR}")
