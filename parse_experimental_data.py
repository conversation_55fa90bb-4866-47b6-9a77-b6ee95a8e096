#!/usr/bin/env python3
"""
Parse Experimental Data
========================
Clean and parse the experimental R/T data files
"""

import pandas as pd
import numpy as np
from pathlib import Path

def parse_experimental_file(filename):
    """Parse a single experimental data file"""
    try:
        # Read the raw data
        data = pd.read_csv(filename)
        
        print(f"\n📁 Parsing {filename}")
        print(f"Raw columns: {data.columns.tolist()}")
        print(f"Raw shape: {data.shape}")
        
        # The first row contains headers, skip it
        data = data.iloc[1:].copy()
        
        # Extract wavelength (first column)
        wavelength_col = data.columns[0]
        wavelengths = pd.to_numeric(data[wavelength_col], errors='coerce')
        
        # The structure is: wavelength, %R, wavelength, %T, wavelength, %R, wavelength, %T
        # We want columns 1 (%R) and 3 (%T) based on the pattern
        r_col = data.columns[1]  # Second column is %R
        t_col = data.columns[3]  # Fourth column is %T

        r_cols = [r_col]
        t_cols = [t_col]
        
        print(f"Found R columns: {r_cols}")
        print(f"Found T columns: {t_cols}")
        
        if len(r_cols) > 0 and len(t_cols) > 0:
            # Create clean dataframe
            R_values = pd.to_numeric(data[r_cols[0]], errors='coerce')
            T_values = pd.to_numeric(data[t_cols[0]], errors='coerce')

            # Check if values are already in percentage (0-100) or fraction (0-1)
            if R_values.max() > 1.0:  # Assume percentage format
                R_values = R_values / 100.0
                T_values = T_values / 100.0
                print("Converting from percentage to fraction")

            clean_data = pd.DataFrame({
                'wavelength': wavelengths,
                'R': R_values,
                'T': T_values
            })
            
            # Remove NaN rows
            clean_data = clean_data.dropna()
            
            print(f"✓ Cleaned data: {len(clean_data)} points")
            print(f"Wavelength range: {clean_data['wavelength'].min():.0f} - {clean_data['wavelength'].max():.0f} nm")
            print(f"R range: {clean_data['R'].min():.4f} - {clean_data['R'].max():.4f}")
            print(f"T range: {clean_data['T'].min():.4f} - {clean_data['T'].max():.4f}")
            
            return clean_data
        else:
            print("❌ Could not identify R and T columns")
            return None
            
    except Exception as e:
        print(f"❌ Error parsing {filename}: {e}")
        return None

def parse_all_experimental_data():
    """Parse all experimental data files"""
    print("🔍 Parsing Experimental Data Files")
    print("=" * 60)
    
    angles = [15, 30, 45, 60]
    parsed_data = {}
    
    for angle in angles:
        filename = f"data/RT_{angle}degree_SP.csv"
        if Path(filename).exists():
            clean_data = parse_experimental_file(filename)
            if clean_data is not None:
                parsed_data[angle] = clean_data
                
                # Save cleaned data
                output_file = f"data/RT_{angle}degree_clean.csv"
                clean_data.to_csv(output_file, index=False)
                print(f"💾 Saved clean data to {output_file}")
        else:
            print(f"⚠ File not found: {filename}")
    
    return parsed_data

def validate_experimental_data(parsed_data):
    """Validate the parsed experimental data"""
    print("\n🔬 Experimental Data Validation")
    print("=" * 60)
    
    for angle, data in parsed_data.items():
        print(f"\n📊 Angle: {angle}°")
        print("-" * 30)
        
        # Basic statistics
        print(f"Data points: {len(data)}")
        print(f"Wavelength range: {data['wavelength'].min():.0f} - {data['wavelength'].max():.0f} nm")
        print(f"R range: {data['R'].min():.4f} - {data['R'].max():.4f}")
        print(f"T range: {data['T'].min():.4f} - {data['T'].max():.4f}")
        
        # Physics validation
        R_plus_T = data['R'] + data['T']
        print(f"R+T range: {R_plus_T.min():.4f} - {R_plus_T.max():.4f}")
        
        # Check for unphysical values
        unphysical_R = ((data['R'] < 0) | (data['R'] > 1)).sum()
        unphysical_T = ((data['T'] < 0) | (data['T'] > 1)).sum()
        unphysical_sum = (R_plus_T > 1.1).sum()  # Allow some measurement error
        
        if unphysical_R > 0:
            print(f"⚠ {unphysical_R} unphysical R values")
        if unphysical_T > 0:
            print(f"⚠ {unphysical_T} unphysical T values")
        if unphysical_sum > 0:
            print(f"⚠ {unphysical_sum} points with R+T > 1.1")
        
        if unphysical_R == 0 and unphysical_T == 0 and unphysical_sum == 0:
            print("✅ All values are physically reasonable")

def create_summary_plot():
    """Create a summary plot of all experimental data"""
    try:
        import matplotlib.pyplot as plt
        
        print("\n📈 Creating Summary Plot")
        print("=" * 30)
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        angles = [15, 30, 45, 60]
        colors = ['blue', 'green', 'orange', 'red']
        
        for i, angle in enumerate(angles):
            filename = f"data/RT_{angle}degree_clean.csv"
            if Path(filename).exists():
                data = pd.read_csv(filename)
                
                ax1.plot(data['wavelength'], data['R'], 
                        color=colors[i], label=f'{angle}°', alpha=0.7)
                ax2.plot(data['wavelength'], data['T'], 
                        color=colors[i], label=f'{angle}°', alpha=0.7)
        
        ax1.set_xlabel('Wavelength (nm)')
        ax1.set_ylabel('Reflectance')
        ax1.set_title('Experimental Reflectance')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        ax2.set_xlabel('Wavelength (nm)')
        ax2.set_ylabel('Transmittance')
        ax2.set_title('Experimental Transmittance')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('experimental_data_summary.png', dpi=150, bbox_inches='tight')
        print("💾 Summary plot saved as 'experimental_data_summary.png'")
        
    except ImportError:
        print("⚠ Matplotlib not available, skipping plot")
    except Exception as e:
        print(f"⚠ Error creating plot: {e}")

if __name__ == "__main__":
    print("🧪 Experimental Data Parser")
    print("=" * 60)
    
    # Parse all data
    parsed_data = parse_all_experimental_data()
    
    # Validate parsed data
    if parsed_data:
        validate_experimental_data(parsed_data)
        create_summary_plot()
        
        print(f"\n✅ Successfully parsed data for {len(parsed_data)} angles")
        print("Clean data files created in data/ directory")
    else:
        print("❌ No experimental data could be parsed")
    
    print("\nNext step: Use clean data for physics validation")
