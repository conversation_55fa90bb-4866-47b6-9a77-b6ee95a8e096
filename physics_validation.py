#!/usr/bin/env python3
"""
Physics Validation Script
=========================
Test fundamental electromagnetic simulation before GAN implementation
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from pathlib import Path

# Check if MEEP is available
try:
    import meep as mp
    MEEP_AVAILABLE = True
    print("✓ MEEP/PyMEEP available")
except ImportError:
    MEEP_AVAILABLE = False
    print("⚠ MEEP not available - using TMM only")

class MaterialDatabase:
    """Load and manage material optical constants"""
    
    def __init__(self):
        self.materials = {}
        self.load_materials()
    
    def load_materials(self):
        """Load material data from files"""
        # Load Al2O3 data
        try:
            al2o3_file = Path("data/Al2O3.txt")
            if al2o3_file.exists():
                data = pd.read_csv(al2o3_file, sep='\s+', header=None, names=['wavelength', 'n', 'k'])
                self.materials['Al2O3'] = data
                print(f"✓ Al2O3: {len(data)} data points loaded")
            else:
                print("⚠ Al2O3.txt not found")
        except Exception as e:
            print(f"⚠ Error loading Al2O3: {e}")

        # Load TiN data
        try:
            tin_file = Path("data/TiN-4nm.xlsx")
            if tin_file.exists():
                data = pd.read_excel(tin_file)
                # Convert wavelength from meters to nm if needed
                if 'Wavelength' in data.columns and data['Wavelength'].max() < 1e-6:  # If in meters
                    data['Wavelength'] = data['Wavelength'] * 1e9  # Convert to nm
                    data.rename(columns={'Wavelength': 'wavelength'}, inplace=True)
                elif 'wavelength' not in data.columns and 'Wavelength' in data.columns:
                    data.rename(columns={'Wavelength': 'wavelength'}, inplace=True)
                # Ensure numeric types
                for col in ['wavelength', 'Wavelength', 'n', 'k']:
                    if col in data.columns:
                        data[col] = pd.to_numeric(data[col], errors='coerce')
                self.materials['TiN'] = data
                print(f"✓ TiN: {len(data)} data points loaded")
            else:
                # Try CSV format
                tin_csv = Path("data/TiN.csv")
                if tin_csv.exists():
                    data = pd.read_csv(tin_csv)
                    # Ensure numeric types
                    for col in data.columns:
                        if col != 'material':  # Skip material name column
                            data[col] = pd.to_numeric(data[col], errors='coerce')
                    self.materials['TiN'] = data
                    print(f"✓ TiN (CSV): {len(data)} data points loaded")
                else:
                    print("⚠ TiN data files not found")
        except Exception as e:
            print(f"⚠ Error loading TiN: {e}")
        
        # Load TiO2 data
        try:
            tio2_file = Path("data/TiO2.csv")
            if tio2_file.exists():
                data = pd.read_csv(tio2_file)
                # Convert to numeric types
                for col in data.columns:
                    data[col] = pd.to_numeric(data[col], errors='coerce')
                # Convert wavelength from micrometers to nm if needed
                if 'wl' in data.columns and data['wl'].max() < 10:  # If in micrometers
                    data['wl'] = data['wl'] * 1000  # Convert to nm
                # Add k column if missing (assume transparent)
                if 'k' not in data.columns:
                    data['k'] = 0.0
                # Rename wl to wavelength if needed
                if 'wl' in data.columns and 'wavelength' not in data.columns:
                    data.rename(columns={'wl': 'wavelength'}, inplace=True)
                self.materials['TiO2'] = data
                print(f"✓ TiO2: {len(data)} data points loaded")
            else:
                # Fallback to literature values
                wavelengths = np.linspace(300, 2600, 100)
                n_tio2 = 2.4 * np.ones_like(wavelengths)  # Approximate constant
                k_tio2 = np.zeros_like(wavelengths)       # Transparent

                self.materials['TiO2'] = pd.DataFrame({
                    'wavelength': wavelengths,
                    'n': n_tio2,
                    'k': k_tio2
                })
                print(f"✓ TiO2: {len(wavelengths)} data points (literature)")
        except Exception as e:
            print(f"⚠ Error loading TiO2: {e}")
    
    def get_refractive_index(self, material, wavelength_nm):
        """Get complex refractive index at specific wavelength"""
        if material not in self.materials:
            print(f"⚠ Material {material} not found, using default")
            return 1.5 + 0j

        data = self.materials[material]

        # Handle different column names
        wl_col = None
        n_col = None
        k_col = None

        # Find wavelength column
        for col in ['wavelength', 'Wavelength', 'wl', 'lambda']:
            if col in data.columns:
                wl_col = col
                break

        # Find n column
        for col in ['n', 'N', 'real', 'n_real']:
            if col in data.columns:
                n_col = col
                break

        # Find k column
        for col in ['k', 'K', 'imag', 'n_imag']:
            if col in data.columns:
                k_col = col
                break

        if wl_col is None or n_col is None or k_col is None:
            print(f"⚠ Column structure issue for {material}: {list(data.columns)}")
            return 1.5 + 0j

        # Interpolate n and k
        n = np.interp(wavelength_nm, data[wl_col], data[n_col])
        k = np.interp(wavelength_nm, data[wl_col], data[k_col])

        return n + 1j * k

class TransferMatrixMethod:
    """Transfer Matrix Method for multilayer structures"""
    
    def __init__(self, material_db):
        self.material_db = material_db
    
    def simulate_multilayer(self, layer_materials, layer_thicknesses, wavelength_nm, angle_deg=0):
        """
        Simulate multilayer structure using TMM
        
        Args:
            layer_materials: List of material names
            layer_thicknesses: List of thicknesses in nm
            wavelength_nm: Wavelength in nm
            angle_deg: Incident angle in degrees
            
        Returns:
            R, T: Reflectance and transmittance
        """
        # Get refractive indices
        n_layers = []
        for material in layer_materials:
            n_complex = self.material_db.get_refractive_index(material, wavelength_nm)
            n_layers.append(n_complex)
        
        # Add air layers (substrate and superstrate)
        n_air = 1.0 + 0j
        n_layers = [n_air] + n_layers + [n_air]
        
        # Convert angle to radians
        theta_0 = np.radians(angle_deg)
        
        # Calculate angles in each layer (Snell's law)
        angles = [theta_0]
        for i in range(1, len(n_layers)):
            sin_theta = (n_layers[0].real * np.sin(theta_0)) / n_layers[i].real
            if abs(sin_theta) > 1:  # Total internal reflection
                angles.append(np.pi/2)
            else:
                angles.append(np.arcsin(sin_theta))
        
        # Wave vector components
        k0 = 2 * np.pi / wavelength_nm  # in nm^-1
        
        # Transfer matrix calculation
        M = np.eye(2, dtype=complex)
        
        for i in range(1, len(n_layers) - 1):  # Skip air layers
            n = n_layers[i]
            d = layer_thicknesses[i-1]  # thickness in nm
            theta = angles[i]
            
            # Phase thickness
            beta = k0 * n * d * np.cos(theta)
            
            # Layer matrix
            cos_beta = np.cos(beta)
            sin_beta = np.sin(beta)
            
            M_layer = np.array([
                [cos_beta, -1j * sin_beta / (n * np.cos(theta))],
                [-1j * n * np.cos(theta) * sin_beta, cos_beta]
            ])
            
            M = M @ M_layer
        
        # Calculate reflection and transmission
        n_sub = n_layers[0]  # Air
        n_exit = n_layers[-1]  # Air
        
        theta_sub = angles[0]
        theta_exit = angles[-1]
        
        # Fresnel coefficients
        Y_sub = n_sub * np.cos(theta_sub)
        Y_exit = n_exit * np.cos(theta_exit)
        
        # Total matrix elements
        m11, m12 = M[0, 0], M[0, 1]
        m21, m22 = M[1, 0], M[1, 1]
        
        # Reflection and transmission coefficients
        r = (Y_sub * m11 + Y_sub * Y_exit * m12 - m21 - Y_exit * m22) / \
            (Y_sub * m11 + Y_sub * Y_exit * m12 + m21 + Y_exit * m22)
        
        t = 2 * Y_sub / (Y_sub * m11 + Y_sub * Y_exit * m12 + m21 + Y_exit * m22)
        
        # Power reflection and transmission
        R = abs(r)**2
        T = (Y_exit.real / Y_sub.real) * abs(t)**2
        
        return R, T

def test_single_layer():
    """Test single layer physics"""
    print("\n🧪 Test 1: Single Layer Physics")
    print("=" * 50)
    
    material_db = MaterialDatabase()
    tmm = TransferMatrixMethod(material_db)
    
    # Test cases
    test_cases = [
        ("Al2O3", 45, 800),  # 45nm Al2O3 at 800nm
        ("TiO2", 45, 800),   # 45nm TiO2 at 800nm
        ("Al2O3", 45, 500),  # 45nm Al2O3 at 500nm
    ]
    
    for material, thickness, wavelength in test_cases:
        R, T = tmm.simulate_multilayer([material], [thickness], wavelength)
        A = 1 - R - T  # Absorption
        
        print(f"\n{material} @ {wavelength}nm, {thickness}nm thick:")
        print(f"  R = {R:.6f}")
        print(f"  T = {T:.6f}")
        print(f"  A = {A:.6f}")
        
        # Physics checks
        if R < 0 or R > 1:
            print(f"  ❌ Unphysical R: {R}")
        if T < 0 or T > 1:
            print(f"  ❌ Unphysical T: {T}")
        if abs(R + T + A - 1) > 0.01:
            print(f"  ❌ Energy not conserved: R+T+A = {R+T+A:.6f}")
        else:
            print(f"  ✓ Energy conserved: R+T+A = {R+T+A:.6f}")

def test_multilayer():
    """Test multilayer structure"""
    print("\n🧪 Test 2: Multilayer Structure")
    print("=" * 50)
    
    material_db = MaterialDatabase()
    tmm = TransferMatrixMethod(material_db)
    
    # 45nm total structure: TiN(4nm) + TiO2(15nm) + Al2O3(15nm) + TiN(11nm)
    materials = ['TiN', 'TiO2', 'Al2O3', 'TiN']
    thicknesses = [4, 15, 15, 11]  # Total = 45nm
    
    wavelengths = [500, 800, 1200, 1600]
    
    print(f"Structure: {' / '.join([f'{m}({t}nm)' for m, t in zip(materials, thicknesses)])}")
    print(f"Total thickness: {sum(thicknesses)}nm")
    
    for wl in wavelengths:
        R, T = tmm.simulate_multilayer(materials, thicknesses, wl)
        A = 1 - R - T
        
        print(f"\n@ {wl}nm:")
        print(f"  R = {R:.6f}")
        print(f"  T = {T:.6f}")
        print(f"  A = {A:.6f}")
        
        # Physics validation
        if abs(R + T + A - 1) > 0.01:
            print(f"  ❌ Energy not conserved")
        else:
            print(f"  ✓ Energy conserved")

def test_angle_dependence():
    """Test angle-dependent response"""
    print("\n🧪 Test 3: Angle Dependence")
    print("=" * 50)
    
    material_db = MaterialDatabase()
    tmm = TransferMatrixMethod(material_db)
    
    angles = [0, 15, 30, 45, 60]
    wavelength = 800
    
    print(f"Al2O3 45nm @ {wavelength}nm - Angle dependence:")
    
    for angle in angles:
        R, T = tmm.simulate_multilayer(['Al2O3'], [45], wavelength, angle)
        print(f"  {angle:2d}°: R = {R:.6f}, T = {T:.6f}")

if __name__ == "__main__":
    print("🔬 Physics Validation Suite")
    print("=" * 60)
    
    # Run validation tests
    test_single_layer()
    test_multilayer()
    test_angle_dependence()
    
    print("\n✅ Physics validation completed!")
    print("Next step: Compare with experimental data")
