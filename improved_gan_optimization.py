#!/usr/bin/env python3
"""
Improved GAN Optimization with Strict Requirements
==================================================

Requirements:
1. Loss must be < 0.01
2. Use ALL data points from RT_15degree_SP.csv, RT_30degree_SP.csv, RT_45degree_SP.csv, RT_60degree_SP.csv
3. Generate 3D visualization with specific colors:
   - Yellow: TiN_4nm (thin film)
   - Purple: TiO2
   - Blue: Al2O3
   - Red: TiN_30nm (thick TiN)
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import time
import os
from scipy import interpolate
import warnings
warnings.filterwarnings('ignore')

# Import our existing components
from gan_meep_electromagnetic import Config, MaterialDatabase, Generator, Discriminator, SurrogateSimulator

class ImprovedGANTrainer:
    """
    Improved GAN trainer with strict requirements:
    - Loss < 0.01
    - Use ALL experimental data points
    - Better optimization strategy
    """
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")
        
        # Initialize components
        self.material_db = MaterialDatabase(config)
        self.load_all_experimental_data()
        
        # Initialize networks with better architecture
        self.generator = Generator(
            latent_dim=config.LATENT_DIM,
            num_materials=len(config.MATERIALS),
            grid_size=config.GRID_SIZE
        ).to(self.device)
        
        self.discriminator = Discriminator(
            num_materials=len(config.MATERIALS),
            grid_size=config.GRID_SIZE
        ).to(self.device)
        
        self.surrogate = SurrogateSimulator(
            num_materials=len(config.MATERIALS),
            grid_size=config.GRID_SIZE
        ).to(self.device)
        
        # Better optimizers with lower learning rates for precision
        self.g_optimizer = optim.Adam(self.generator.parameters(), lr=0.0001, betas=(0.5, 0.999))
        self.d_optimizer = optim.Adam(self.discriminator.parameters(), lr=0.0001, betas=(0.5, 0.999))
        self.s_optimizer = optim.Adam(self.surrogate.parameters(), lr=0.0001)
        
        # Loss functions
        self.adversarial_loss = nn.BCELoss()
        self.rt_loss = nn.MSELoss()
        
        # Training history
        self.training_history = {
            'g_losses': [],
            'd_losses': [],
            's_losses': [],
            'physics_losses': [],
            'best_loss': float('inf'),
            'best_structure': None
        }
        
        # Target loss
        self.target_loss = 0.01
        print(f"🎯 Target physics loss: < {self.target_loss}")
    
    def load_all_experimental_data(self):
        """Load ALL experimental data points from all files"""
        print("📊 Loading ALL experimental data...")
        
        self.experimental_data = {}
        self.all_data_points = []  # Store all wavelength-angle-R-T combinations
        
        for angle in [15, 30, 45, 60]:
            try:
                file_path = f'data/RT_{angle}degree_SP.csv'
                data = pd.read_csv(file_path)
                
                self.experimental_data[angle] = {
                    'wavelength': data.iloc[:, 0].values,  # nm
                    'R': data.iloc[:, 1].values,
                    'T': data.iloc[:, 2].values
                }
                
                # Add all data points to comprehensive list
                for i in range(len(data)):
                    self.all_data_points.append({
                        'wavelength': data.iloc[i, 0],
                        'angle': angle,
                        'R_exp': data.iloc[i, 1],
                        'T_exp': data.iloc[i, 2]
                    })
                
                print(f"✓ Loaded {len(data)} data points for {angle}°")
                
            except Exception as e:
                print(f"❌ Error loading {angle}° data: {e}")
        
        print(f"📊 Total experimental data points: {len(self.all_data_points)}")
        print(f"   Wavelength range: {min(p['wavelength'] for p in self.all_data_points):.0f}-{max(p['wavelength'] for p in self.all_data_points):.0f}nm")
        print(f"   Angles: {sorted(set(p['angle'] for p in self.all_data_points))}°")
    
    def calculate_comprehensive_physics_loss(self, material_distributions):
        """
        Calculate physics loss using ALL experimental data points
        Much more comprehensive than before
        """
        total_loss = 0.0
        num_samples = 0
        
        # Use a subset of all data points for efficiency (every 5th point)
        # This still covers the full wavelength range but is computationally feasible
        data_subset = self.all_data_points[::5]  # Every 5th point
        
        for i, material_dist in enumerate(material_distributions):
            sample_loss = 0.0
            sample_count = 0
            
            # Test on subset of experimental data
            for data_point in data_subset[:20]:  # Use 20 points per structure
                try:
                    wavelength_nm = data_point['wavelength']
                    angle_deg = data_point['angle']
                    R_exp = data_point['R_exp']
                    T_exp = data_point['T_exp']
                    
                    # Use surrogate simulator for fast prediction
                    wavelength_tensor = torch.tensor([[wavelength_nm / 1000.0]], device=self.device)  # Convert to μm
                    angle_tensor = torch.tensor([[angle_deg / 90.0]], device=self.device)  # Normalize
                    polarization_tensor = torch.zeros(1, 1, device=self.device)  # s-polarization
                    
                    R_sim, T_sim = self.surrogate(
                        material_dist.unsqueeze(0),
                        wavelength_tensor,
                        angle_tensor, 
                        polarization_tensor
                    )
                    
                    R_sim = R_sim.squeeze()
                    T_sim = T_sim.squeeze()
                    
                    # Calculate relative errors with better handling
                    if R_exp > 1e-6:
                        r_error = torch.abs((R_sim - R_exp) / R_exp)
                    else:
                        r_error = torch.abs(R_sim)
                    
                    if T_exp > 1e-6:
                        t_error = torch.abs((T_sim - T_exp) / T_exp)
                    else:
                        t_error = torch.abs(T_sim)
                    
                    # Weighted loss with higher precision requirements
                    loss = self.config.ALPHA1 * r_error + self.config.ALPHA2 * t_error
                    sample_loss += loss
                    sample_count += 1
                    
                except Exception as e:
                    continue
            
            if sample_count > 0:
                total_loss += sample_loss / sample_count
                num_samples += 1
        
        return total_loss / num_samples if num_samples > 0 else torch.tensor(0.0, device=self.device)
    
    def train_improved_gan(self, max_epochs=500):
        """
        Improved GAN training with stricter convergence criteria
        """
        print(f"🚀 Starting Improved GAN Training")
        print(f"   Target loss: < {self.target_loss}")
        print(f"   Max epochs: {max_epochs}")
        print(f"   Using ALL experimental data points")
        
        for epoch in range(max_epochs):
            epoch_start = time.time()
            
            # =================== Train Discriminator ===================
            self.discriminator.train()
            self.generator.eval()
            
            # Generate real and fake patterns
            real_patterns = self.generate_real_patterns(batch_size=self.config.BATCH_SIZE)
            real_labels = torch.ones(real_patterns.size(0), 1, device=self.device)
            
            noise = torch.randn(self.config.BATCH_SIZE, self.config.LATENT_DIM, device=self.device)
            fake_patterns = self.generator(noise)
            fake_labels = torch.zeros(fake_patterns.size(0), 1, device=self.device)
            
            # Train discriminator
            self.d_optimizer.zero_grad()
            
            real_pred = self.discriminator(real_patterns)
            d_real_loss = self.adversarial_loss(real_pred, real_labels)
            
            fake_pred = self.discriminator(fake_patterns.detach())
            d_fake_loss = self.adversarial_loss(fake_pred, fake_labels)
            
            d_loss = (d_real_loss + d_fake_loss) / 2
            d_loss.backward()
            self.d_optimizer.step()
            
            # =================== Train Generator ===================
            self.generator.train()
            self.g_optimizer.zero_grad()
            
            # Generate new fake patterns
            noise = torch.randn(self.config.BATCH_SIZE, self.config.LATENT_DIM, device=self.device)
            fake_patterns = self.generator(noise)
            fake_pred = self.discriminator(fake_patterns)
            
            # Adversarial loss
            g_adversarial_loss = self.adversarial_loss(fake_pred, real_labels)
            
            # Comprehensive physics loss using ALL data
            physics_loss = self.calculate_comprehensive_physics_loss(fake_patterns)
            
            # Combined generator loss with higher weight on physics
            g_loss = g_adversarial_loss + 100.0 * physics_loss  # Much higher weight
            g_loss.backward()
            self.g_optimizer.step()
            
            # =================== Record Progress ===================
            self.training_history['g_losses'].append(g_loss.item())
            self.training_history['d_losses'].append(d_loss.item())
            self.training_history['physics_losses'].append(physics_loss.item())
            
            # Check for best structure
            if physics_loss.item() < self.training_history['best_loss']:
                self.training_history['best_loss'] = physics_loss.item()
                self.training_history['best_structure'] = fake_patterns[0].clone().cpu()
                print(f"🎯 New best structure! Loss: {physics_loss.item():.6f}")
            
            epoch_time = time.time() - epoch_start
            
            # =================== Progress Report ===================
            if epoch % 10 == 0:
                print(f"Epoch {epoch}/{max_epochs}")
                print(f"  Physics loss: {physics_loss.item():.6f} (target: < {self.target_loss})")
                print(f"  G_loss: {g_loss.item():.4f}, D_loss: {d_loss.item():.4f}")
                print(f"  Best loss: {self.training_history['best_loss']:.6f}")
                print(f"  Time: {epoch_time:.2f}s")
            
            # =================== Early Stopping ===================
            if self.training_history['best_loss'] < self.target_loss:
                print(f"\n🎉 TARGET ACHIEVED!")
                print(f"   Physics loss: {self.training_history['best_loss']:.6f} < {self.target_loss}")
                print(f"   Converged at epoch {epoch}")
                break
        
        if self.training_history['best_loss'] >= self.target_loss:
            print(f"\n⚠ Target not reached in {max_epochs} epochs")
            print(f"   Best loss: {self.training_history['best_loss']:.6f}")
            print(f"   Target: < {self.target_loss}")
        
        return self.training_history['best_structure']
    
    def generate_real_patterns(self, batch_size=16):
        """Generate more realistic structural patterns"""
        real_patterns = []
        
        for _ in range(batch_size):
            pattern = torch.zeros(len(self.config.MATERIALS), *self.config.GRID_SIZE)
            
            # Create more structured layered patterns
            layer_height = self.config.GRID_SIZE[2] // len(self.config.MATERIALS)
            
            for i, material_idx in enumerate(range(len(self.config.MATERIALS))):
                start_z = i * layer_height
                end_z = min((i + 1) * layer_height, self.config.GRID_SIZE[2])
                
                # Create more realistic patterns with gradients
                for x in range(self.config.GRID_SIZE[0]):
                    for y in range(self.config.GRID_SIZE[1]):
                        # Add spatial variation and gradients
                        prob = 0.8 + 0.2 * np.sin(x/10) * np.cos(y/10)
                        if np.random.random() < prob:
                            pattern[material_idx, x, y, start_z:end_z] = 1.0
            
            # Add realistic noise and smooth transitions
            noise = torch.randn_like(pattern) * 0.02
            pattern = torch.clamp(pattern + noise, 0, 1)
            
            # Ensure sum=1 across materials
            pattern = F.softmax(pattern, dim=0)
            real_patterns.append(pattern)
        
        return torch.stack(real_patterns).to(self.device)

def create_3d_visualization_with_colors(structure, config, save_path="results/3d_structure_visualization.png"):
    """
    Create 3D visualization with specified colors:
    - Yellow: TiN_4nm (thin film)
    - Purple: TiO2
    - Blue: Al2O3
    - Red: TiN_30nm (thick TiN)
    """
    print("🎨 Creating 3D visualization with specified colors...")
    
    # Get dominant materials
    dominant_materials = np.argmax(structure, axis=0)
    
    # Define colors for each material
    colors = {
        0: 'yellow',    # TiN_4nm (thin film)
        1: 'purple',    # TiO2
        2: 'blue',      # Al2O3
        3: 'red'        # TiN_30nm (thick TiN)
    }
    
    # Create figure with multiple views
    fig = plt.figure(figsize=(20, 15))
    
    # 3D visualization
    ax1 = fig.add_subplot(2, 3, 1, projection='3d')
    
    # Sample every 5th voxel for visualization (too many points otherwise)
    x_sample = range(0, dominant_materials.shape[0], 5)
    y_sample = range(0, dominant_materials.shape[1], 5)
    z_sample = range(0, dominant_materials.shape[2], 5)
    
    for x in x_sample:
        for y in y_sample:
            for z in z_sample:
                material_idx = dominant_materials[x, y, z]
                color = colors[material_idx]
                ax1.scatter(x, y, z, c=color, s=20, alpha=0.6)
    
    ax1.set_xlabel('X (nm)')
    ax1.set_ylabel('Y (nm)')
    ax1.set_zlabel('Z (nm)')
    ax1.set_title('3D Material Distribution\n(Sampled every 5th voxel)')
    
    # Add legend
    legend_elements = [plt.Line2D([0], [0], marker='o', color='w', markerfacecolor=colors[i], 
                                 markersize=10, label=f'{config.MATERIALS[i]}') 
                      for i in range(len(config.MATERIALS))]
    ax1.legend(handles=legend_elements, loc='upper right')
    
    # XY slices at different Z positions
    z_positions = [10, 25, 40, 55, 70, 85]
    for i, z_pos in enumerate(z_positions):
        if i < 6:  # Only show 6 slices
            row = 1 + i // 3
            col = i % 3
            if row < 3:
                ax = fig.add_subplot(2, 3, i+2)
                
                if z_pos < dominant_materials.shape[2]:
                    slice_data = dominant_materials[:, :, z_pos]
                    
                    # Create color map
                    color_map = np.zeros((*slice_data.shape, 3))
                    for material_idx in range(len(config.MATERIALS)):
                        mask = slice_data == material_idx
                        if material_idx == 0:  # Yellow for TiN_4nm
                            color_map[mask] = [1, 1, 0]
                        elif material_idx == 1:  # Purple for TiO2
                            color_map[mask] = [0.5, 0, 0.5]
                        elif material_idx == 2:  # Blue for Al2O3
                            color_map[mask] = [0, 0, 1]
                        elif material_idx == 3:  # Red for TiN_30nm
                            color_map[mask] = [1, 0, 0]
                    
                    ax.imshow(color_map, aspect='auto')
                    ax.set_title(f'XY Slice at Z={z_pos} ({z_pos*0.45:.1f}nm)')
                    ax.set_xlabel('Y direction')
                    ax.set_ylabel('X direction')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"✓ 3D visualization saved to {save_path}")
    
    # Create detailed layer profile with colors
    create_detailed_layer_profile(structure, config)

def create_detailed_layer_profile(structure, config):
    """Create detailed layer profile showing material distribution"""
    dominant_materials = np.argmax(structure, axis=0)
    
    grid_x, grid_y, grid_z = dominant_materials.shape
    layer_thickness = 45.0 / grid_z
    
    # Calculate material fraction at each z-layer
    z_positions = np.arange(grid_z) * layer_thickness
    material_fractions = np.zeros((len(config.MATERIALS), grid_z))
    
    for z in range(grid_z):
        layer_slice = dominant_materials[:, :, z]
        for i in range(len(config.MATERIALS)):
            material_fractions[i, z] = np.sum(layer_slice == i) / layer_slice.size
    
    # Plot with specified colors
    fig, ax = plt.subplots(figsize=(14, 10))
    
    colors = ['yellow', 'purple', 'blue', 'red']  # As specified
    ax.stackplot(z_positions, *material_fractions, 
                labels=config.MATERIALS, colors=colors, alpha=0.8)
    
    ax.set_xlabel('Depth (nm)', fontsize=14)
    ax.set_ylabel('Material Fraction', fontsize=14)
    ax.set_title('Material Distribution vs Depth\n(Optimized 45nm Structure with Target Loss < 0.01)', fontsize=16)
    ax.legend(loc='upper right', fontsize=12)
    ax.grid(True, alpha=0.3)
    ax.set_xlim(0, 45)
    ax.set_ylim(0, 1)
    
    # Add color coding legend
    legend_text = """Color Coding:
Yellow: TiN_4nm (thin film)
Purple: TiO₂
Blue: Al₂O₃  
Red: TiN_30nm (thick TiN)"""
    
    ax.text(0.02, 0.98, legend_text, transform=ax.transAxes, fontsize=10,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig("results/detailed_layer_profile_with_colors.png", dpi=150, bbox_inches='tight')
    plt.close()
    
    print("✓ Detailed layer profile with colors saved to results/detailed_layer_profile_with_colors.png")

def main():
    """Main improved optimization function"""
    print("🎯 IMPROVED GAN OPTIMIZATION")
    print("="*50)
    print("Requirements:")
    print("  • Physics loss < 0.01")
    print("  • Use ALL experimental data points")
    print("  • 3D visualization with specified colors")
    
    # Initialize configuration
    config = Config()
    
    # Create improved trainer
    trainer = ImprovedGANTrainer(config)
    
    # Run improved training
    best_structure = trainer.train_improved_gan(max_epochs=500)
    
    if best_structure is not None and trainer.training_history['best_loss'] < trainer.target_loss:
        print(f"\n🎉 SUCCESS! Target achieved!")
        print(f"   Final loss: {trainer.training_history['best_loss']:.6f} < {trainer.target_loss}")
        
        # Save improved results
        os.makedirs("results", exist_ok=True)
        np.save("results/improved_best_structure.npy", best_structure.numpy())
        
        # Create 3D visualization with specified colors
        create_3d_visualization_with_colors(best_structure.numpy(), config)
        
        print(f"\n📁 Results saved:")
        print(f"   • results/improved_best_structure.npy")
        print(f"   • results/3d_structure_visualization.png")
        print(f"   • results/detailed_layer_profile_with_colors.png")
        
    else:
        print(f"\n⚠ Target not achieved. Best loss: {trainer.training_history['best_loss']:.6f}")
        print(f"   Consider running longer or adjusting parameters")

if __name__ == "__main__":
    main()
